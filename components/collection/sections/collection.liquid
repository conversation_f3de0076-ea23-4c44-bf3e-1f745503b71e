{% render 'localized-prices' %}

<div class="collection{% for block in section.blocks %} collection--{{ block.type }}{% endfor %}">

  <section class="flex flex-col">
    {% if section.settings.show_title and collection.title != blank %}
      <h1 class="h5 text-center border-b-2 border-white m-0 p-1">{{ collection.title }}</h1>
    {% endif %}

    <div class="collection-tools-container">
      {% if section.settings.show_tools %}
        <div class="collection-tools overflow-hidden relative" neptune-liquid="{topic:Collection}">
          <template>
          {% raw %}
            {% assign category = '{% endraw %}{{ section.settings.exposed_filter_set }}{% raw %}' | split: ', ' %}
            {% assign style = '{% endraw %}{{ section.settings.exposed_filter_style }}{% raw %}' %}

            {% if style == 'images' or cart.attributes['segment__test__exposed-filter-style'] == 'images' %}
              <div class="top-facets swiper-container bg-white empty:hidden"
                  neptune-swiper="
                  {
                    initialSlide: {{ collection.elements.exposedFilters.initialSlide | default: 0 }},
                    centeredSlides: false,
                    slidesPerView: 'auto', 
                    spaceBetween: 10,
                    navigation: {
                      prevEl: '.swiper-button-prev-unique',
                      nextEl: '.swiper-button-next-unique',
                      disabledClass: 'swiper-button-disabled'
                    },
                    keyboard: {
                      enabled: true,
                      onlyInViewport: true
                    },
                    cssMode: true
                  }
              ">
                <div class="swiper-wrapper pt-2.5 pb-1 empty:hidden">
                  {%- assign count = 0 -%}
                  {%- for set in collection.filters.all -%}
                    {%- for item in category -%}
                      {%- if item contains set.label -%}
                        {%- for option in set.options -%}

                          {% assign count = count | plus: 1 %}
                          {% assign option_handle = option.label | handle %}
                          {% assign option_image = imageMap[option_handle] %}

                          {% if option.active %}
                            <label class="swiper-slide flex-shrink-0 cursor-pointer flex flex-col items-center whitespace-nowrap w-[88px] lg:w-[117px] MonumentGrotesk-Bold first:scroll-ml-3.5 last:scroll-mr-3.5 first:ml-3.5 last:mr-3.5">
                              <input class="sr-only hidden" value="{{ set.key }}" type="checkbox" oninput="Collection.filter('{{ set.key }}', '{{ option.value }}', this.closest('.swiper-container').swiper)" checked>

                              <div class="relative overflow-hidden rounded-[15px] w-full aspect-[117/175] bg-secondary border border-black">
                          {% else %}
                            <label class="swiper-slide flex-shrink-0 cursor-pointer flex flex-col items-center whitespace-nowrap w-[88px] lg:w-[117px] first:scroll-ml-3.5 last:scroll-mr-3.5 first:ml-3.5 last:mr-3.5">
                              <input class="sr-only hidden" value="{{ set.key }}" type="checkbox" oninput="Collection.filter('{{ set.key }}', '{{ option.value }}', this.closest('.swiper-container').swiper)">

                              <div class="relative overflow-hidden rounded-[15px] w-full aspect-[117/175] bg-secondary border border-[#E5E6E9]">
                          {% endif %}
                                
                                {% if option_image != blank %}
                                  {%- capture option_image_url -%}
                                    {{ option_image }}{% if option_image contains '?' %}&{% else %}?{% endif %}width=
                                  {%- endcapture -%}
                                  <img src="{{ option_image_url | append: 200 }}" loading="lazy" class="absolute inset-0 w-full h-full object-cover" 
                                    srcset="{{ option_image_url | append: 400 }} 2x, {{ option_image_url | append: 600 }} 3x">
                                {% endif %}
                              </div>

                              <span class="uppercase text-[11px] leading-normal tracking-[.55px] p-2">{{ option.label | replace:'_', ' ' }}</span>
                            </label>

                        {%- endfor -%}
                      {%- endif -%}
                    {%- endfor -%}
                  {%- endfor -%}
                </div>

                {%- if count > 0 -%}
                  <button class="swiper-mobile-nav swiper-prev swiper-button-prev-unique btn-control btn-control--dark p-2" tabindex="0" aria-label="Prev slide">
                    {% endraw %}{% render 'icon' icon:'chevron-left' width:30 height:30 %}{% raw %}
                  </button>
                  <button class="swiper-mobile-nav swiper-next swiper-button-next-unique btn-control btn-control--dark p-2" tabindex="0" aria-label="Next slide">
                    {% endraw %}{% render 'icon' icon:'chevron-right' width:30 height:30 %}{% raw %}
                  </button>
                {%- endif -%}
              </div>

            {% elsif style == 'buttons' or cart.attributes['segment__test__exposed-filter-style'] == 'buttons' %}
              <div class="top-facets flex gap-2.5 p-3.5 border-b-2 border-white overflow-x-auto scrollbar-hide empty:hidden">
                
                {%- for set in collection.filters.all -%}
                  {%- for item in category -%}
                    {%- if item contains set.label -%}
                      {%- for option in set.options -%}
                        
                        {% if option.active %}
                          <label for="{{ set.key }}--{{ forloop.index }}" class="button button--light cursor-pointer flex items-center px-3.5 whitespace-nowrap bg-gray-cool" tabindex="0" onkeydown="if(event.key === 'Enter' || event.key === ' ') { event.preventDefault(); document.querySelector('#{{ set.key }}--{{ forloop.index }}').dispatchEvent(new Event('input')); }">
                            <input id="{{ set.key }}--{{ forloop.index }}" class="sr-only hidden" value="{{ set.key }}" type="checkbox" oninput="Collection.filter('{{ set.key }}', '{{ option.value }}')" checked>
                        {% else %}
                          <label for="{{ set.key }}--{{ forloop.index }}" class="button button--light cursor-pointer flex items-center px-3.5 whitespace-nowrap" tabindex="0" onkeydown="if(event.key === 'Enter' || event.key === ' ') { event.preventDefault(); document.querySelector('#{{ set.key }}--{{ forloop.index }}').dispatchEvent(new Event('input')); }">
                            <input id="{{ set.key }}--{{ forloop.index }}" class="sr-only hidden" value="{{ set.key }}" type="checkbox" oninput="Collection.filter('{{ set.key }}', '{{ option.value }}')">
                        {% endif %}
                            <span class="field__button-text">{{ option.label | replace:'_', ' ' }}</span>
                          </label>

                      {%- endfor -%}
                    {%- endif -%}
                  {%- endfor -%}
                {%- endfor -%}

              </div>
    
            {% endif %}
          {% endraw %}
          </template>
        </div>

        <header class="collection-sort w-full flex justify-between items-center gap-4 border-b-2 border-white mb-0 px-4 py-1.5 relative">
          <div class="flex items-center gap-4">
            <button id="filtersToggle" type="button" class="collection__filters--toggle {{ section.settings.filter_trigger_style }} flex tracking-wider" 
              neptune-engage="{targets:[
                {
                  attributes:[{
                    att:data-return-focus 
                    set:active
                  }]
                },
                {
                  selector:html,
                  attributes:[{
                    att:data-active-modal,
                    set:filters
                  }]
                }
              ]}"
            >
              <div class="hidden">
                <span>{{ section.settings.filter_trigger_text }}</span>
                <span class="ml-1"><svg width="9" height="5" viewBox="0 0 9 5" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M0 0L4.5 5L9 0H0Z" fill="currentColor"></path></svg></span>				
              </div>
              <svg width="37" height="17" viewBox="0 0 37 17" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M11.9008 2.45265C11.6435 1.06962 10.4405 0.0229492 8.99339 0.0229492C7.54631 0.0229492 6.34332 1.06962 6.08601 2.45265H0.0229492V3.58901H6.09142C6.36075 4.95689 7.55713 5.9884 8.99339 5.9884C10.4296 5.9884 11.6266 4.95689 11.8954 3.58901H36.6201V2.45265H11.9008Z" fill="#1D1D1B"></path>			<path d="M8.99328 6.01151C7.58047 6.01151 6.35524 5.00364 6.07267 3.61212H0V2.4303H6.06726C6.3384 1.02061 7.56604 0 8.99328 0C10.4205 0 11.6482 1.02061 11.9193 2.4297H36.6429V3.61151H11.9139C11.6313 5.00303 10.4061 6.01091 8.99328 6.01091V6.01151ZM0.0450897 3.56606H6.10995L6.11356 3.58424C6.3853 4.96424 7.5961 5.96545 8.99328 5.96545C10.3905 5.96545 11.6019 4.96424 11.873 3.58424L11.8766 3.56606H36.5972V2.47515H11.882L11.8784 2.45636C11.6187 1.05939 10.4055 0.0454545 8.99328 0.0454545C7.58107 0.0454545 6.36786 1.05939 6.10814 2.45697L6.10454 2.47576H0.0450897V3.56667V3.56606Z" fill="#1D1D1B"></path>			<path d="M27.6497 11.0115C26.208 11.0115 25.008 12.0509 24.7447 13.426H0.0229492V14.5624H24.7447C25.008 15.9381 26.208 16.9769 27.6497 16.9769C29.0913 16.9769 30.2913 15.9375 30.5547 14.5624H36.6207V13.426H30.5547C30.2913 12.0503 29.0913 11.0115 27.6497 11.0115Z" fill="#1D1D1B"></path>			<path d="M27.6496 16.9999C26.2296 16.9999 25.0031 15.9854 24.7266 14.5854H0V13.4036H24.726C25.0031 12.003 26.2295 10.989 27.649 10.989C29.0684 10.989 30.2954 12.0036 30.572 13.4036H36.6423V14.5854H30.572C30.2948 15.986 29.0684 16.9999 27.649 16.9999H27.6496ZM0.0450896 14.5399H24.7632L24.7668 14.5581C25.0326 15.9466 26.2446 16.9545 27.6496 16.9545C29.0546 16.9545 30.2666 15.9466 30.5323 14.5581L30.5359 14.5399H36.5984V13.449H30.5359L30.5323 13.4308C30.2666 12.0423 29.0546 11.0345 27.6496 11.0345C26.2446 11.0345 25.0326 12.0423 24.7668 13.4308L24.7632 13.449H0.0450896V14.5399Z" fill="#1D1D1B"></path></svg>
            </button>

            <div class="uppercase text-2xs font-highlight tracking-wider flex lg:flex-row flex-col lg:gap-4 max-w-1/2" neptune-liquid="{topic:Collection}">
              <template>
                {% raw %}
                  {% endraw %}{% if template contains 'search' %}{% raw %}
                  <span class="breadcrumbs__links pv0 tracked-slight py-0 tracking-wide">
                    <span class="hidden lg:inline"><a href="/">Home</a></span>
                    <span><span class="hidden lg:inline"> > Search </span>Results for '{{ collection.params.q }}'</span>
                  </span> 
                  {% endraw %}{% endif %}{% raw %}
                  <span>{{ collection.total_products }} Product{% if collection.total_products > 1 %}s{% endif %}</span>
                {% endraw %}
              </template>
            </div>
          </div>

          <div class="flex items-center gap-4">
            {% if section.settings.sort != blank %}
              {% assign sorters = section.settings.sort | newline_to_br | split:'<br />' %}

              <div>
                <div class="collection__tools--sort list-none" neptune-engage="{
                  on:click,
                  targets: [
                    {
                      selector: '.collection__sort',
                      classes:toggle:open,
                      siblings:classes:remove:open
                    }
                  ]
                }">
                  <label class="uppercase m-0 flex items-center p-0 cursor-pointer">					
                    <span>Sort By</span>					
                    <span class="ml-1"><svg width="9" height="5" viewBox="0 0 9 5" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M0 0L4.5 5L9 0H0Z" fill="currentColor"></path></svg></span>				
                  </label>  
                </div>

                <div class="collection__sort">
                  <ul class="m-0 p-0 list-none" style="--sort-width:{{section.settings.sort_width}}px;">
                    {% for sorter in sorters %}
                      <li><div role="button" class="block cursor-pointer w-full{% if collection.sort_by == 'featured' %} font-bold{% endif %}" onclick="Collection.sort('{{ sorter | split: ':' | first | remove: "\n" | strip }}');Array.from(this.parentNode.parentNode.children).forEach(li => li.querySelector('div').classList.remove('font-bold')); this.classList.add('font-bold');">{{ sorter | split: ':' | last | remove: "\n" | strip }}</div></li>
                    {% endfor %}
                  </ul>        
                </div>
              </div>
            {% endif %}
          </div>
        </header>
        
        <div class="collection-filters--selected relative overflow-hidden">
          <div class="flex gap-1.5 py-[.375rem] px-[.625rem] border-b-2 border-white overflow-x-auto scrollbar-hide empty:hidden" neptune-liquid="{topic:Collection}">
            <template>
            {% raw %}
              {%- for filter in collection.filters.all -%}
                {%- for value in filter.options -%}
                  {%- if collection.filters.applied[filter.key] and collection.filters.applied[filter.key] contains value.value -%}
                  <span class="collection__summary--filter">
                    {% assign label = value.label | trim | replace: '_', ' ' %}
                    {% assign value_label = filterItemNameOverrides[label] %}
                    {% if value_label == blank %}
                      {% assign value_label = label %}
                    {% endif %}
                    {{ value_label }}
                    <button type="button" class="ml-1 align-middle mb-px" onclick="Collection.filter('{{ filter.key }}', '{{ value.value }}', true)">
                      {% endraw %}{% render 'icon', icon: 'x' width:12 height:12 strokeWidth:2 %}{% raw %}
                    </button>
                  </span>
                  {%- endif -%}
                {%- endfor -%}
              {%- endfor -%}
            {% endraw %}
            </template>
          </div>
        </div>
      {% endif %}
    </div>

    <main
      neptune-liquid="{topic:Collection,append:[collectionBanners]}"
      product-grid
      class="collection__product-grid grid {{ section.settings.product_items_per_row }} {{ section.settings.product_items_per_row_mobile }} has-skeleton"
    >
      <template>
        {% raw %}
          
          {% if collection.loading %}
            
              {% for i in (1..12)  %}
                {% endraw %}{% render 'product-item-skeleton' %}{% raw %}
              {% endfor %}
            
          {% else %}

            {% for product in collection.products %}
              
              {% capture banner %}.collection-banner-template[data-position="{{ product.position }}"]{% endcapture %}
              {% import banner %}

              {% endraw %}
              {% render 'product-item' action:'quick-add-toggle' collection:collection.handle alt_image_term:section.settings.image_override_term show_swatch:section.settings.show_swatch excluded_item_badge:true %}
              {% raw %}

            {% endfor %}

          {% endif %}

        {% endraw %}
      </template>

      {% for i in (1..12)  %}
        {% render 'product-item-skeleton' %}
      {% endfor %}

    </main>

    {% if section.settings.show_tools %}
      <aside
        data-modal="filters"
        class="collection__filters filter-{{ section.settings.filter_options_style_default }} fixed top-0 right-0 z-50  lg:w-1/3 w-11/12 h-screen max-h-full lg:max-h-screen transition-transform duration-300 ease-in-out bg-light modal-right animate overflow-y-scroll"
        neptune-liquid="{topic:Collection}"
        tabindex="-1" role="dialog" aria-modal="true" aria-label="Collection Filters" neptune-brig="{
          'targets': [
            'button:not([disabled])',
            'details summary',
            'details[open] label.accordion-label'
          ]
        }"
      >
        <template>
          {% render 'collection-filters' exclusion:section.settings.filter_exclusions, config:section.settings %}
        </template>
      </aside>
    {% endif %}

    {% if section.settings.paging == 'paginated' %}
      <footer class="collection__pagination w-full" 
        neptune-liquid="{topic:Collection}">
        <template>
        {% raw %}
          {% if collection.pagination.total_pages > 1 %}
            <nav class="flex justify-center gap-2 my-8">
              {% assign current_page = collection.pagination.current_page %}
              
              {% if current_page > 1 %}
                <button class="pagination__arrow--prev w-6 h-6 flex justify-center items-center" onclick="event.preventDefault(); this.blur();  Collection.page({{ current_page | minus: 1 }},1)">
                  {% endraw %}
                  {% render 'icon' icon:'arrow-left' width: 16 %}
                  {% raw %}
                </button>
              {% endif %}

              {% for page in collection.pagination.pages %}
                {% assign active_classes = '' %}
                {% if page.number == current_page %}
                  {% assign active_classes = 'bg-black text-white' %}
                {% endif %}
                <button 
                  class="w-6 h-6 text-center rounded-full {{ active_classes }}" 
                  onclick="event.preventDefault(); this.blur();  Collection.page({{ page.number}},1)"
                >
                  {{ page.number }}
                </button>
              {% endfor %}

              {% if current_page < collection.pagination.pages.size %}

              <button class="pagination__arrow--next w-6 h-6 flex justify-center items-center" onclick="event.preventDefault(); this.blur();  Collection.page({{current_page | plus: 1}},1); return false;">
                {% endraw %}
                {% render 'icon' icon:'arrow-right' width: 16 %}
                {% raw %}
              </button>

              {% endif %}
            </nav>
          {% endif %}
        {% endraw %}
        </template>
      </footer>
    {% endif %}  
    
  </section>

</div>

<script type="text/javascript">
  window.colorMap = {}
  {% assign colors = section.settings.filter_color_map | newline_to_br | split: '<br />' %}
  {% for color in colors %}
    {% assign c = color | split: ':' %}
    colorMap['{{ c | first | handle }}'] = {{ c | last | strip | json }};
  {% endfor %}

  window.imageMap = {}
  {% assign images = section.settings.filter_image_map | newline_to_br | split: '<br />' %}
  {% for image in images %}
    {% assign i = image | split: ':' %}
    imageMap['{{ i | first | handle }}'] = {{ i | last | strip | json }};
  {% endfor %}

  window.filterMap = {}
  {% assign filter_styles = section.settings.filter_options_style_map | newline_to_br | split: '<br />' %}
  {% for filter in filter_styles %}
    {% assign i = filter | split: ':' %}
    filterMap['{{ i | first | handle }}'] = {{ i | last | handle | json }};
  {% endfor %}

  window.filterItemNameOverrides = {};
  {% assign filter_items = section.settings.filter_item_override_map | newline_to_br | strip_newlines | split: '<br />' %}
  {% for override in filter_items %}
    {% assign existing_label = override | split: ':' | first %}
    {% assign new_label = override | split: ':' | last %}

    window.filterItemNameOverrides['{{ existing_label | escape }}'] = '{{ new_label | escape }}';
  {% endfor %}

  window.collection = {
    settings: {{ section.settings | json }}
  };
  collection.settings.remote = []

  {% for block in section.blocks %}
    {% case block.type %}

      {% when 'data-remote' %}
        collection.settings.remote = {{ block.settings | json }}
        collection.settings.remote_url = '{{ block.settings.remote_url }}';
        collection.settings.map = {{ block.settings.map | default: 'false' }};

      {% when 'data-shopify' %}
        {% render 'shopify-collection-settings' collection:collection section:section %}

      {% when 'data-searchspring' %}
        {% render 'searchspring-collection-settings' collection:collection section:section %}

    {% endcase %}
  {% endfor %}

  window.addEventListener('Collection:loaded', (e) => {
    Neptune.liquid.load('Collection');
    Neptune.uncomment.init(_n.qs('[product-grid]'));
  });
</script>

{{ 'collection-filter-sort.js' | asset_url | script_tag }}
{{ 'collection.js' | asset_url | script_tag }}
{{ 'collection-endless.js' | asset_url | script_tag }}

{% style %}
  {% if section.settings.background_color != blank %}
    .collection {
      background-color: {{ section.settings.background_color }};
    }
  {% endif %}

  {% if section.settings.item_height != '' %}
    .product-item__image--hover, .product-item__image {
      --tw-aspect-w: {{ section.settings.item_height | split: '|' | first }};
      --tw-aspect-h: {{ section.settings.item_height | split: '|' | last }};
    }
  {% endif %}

  {% if section.settings.image_display == 'bg-cover' %}
    .product-item__image--main {
      object-fit: cover;
    }
    .product-item__image--hover {
      background-color: var(--color-light) !important;
    }
  {% endif %}

  {% if section.settings.btn_color != blank or section.settings.btn_text_color != blank or section.settings.btn_border_color != blank %}
    .quick-add__button {
      {% if section.settings.btn_color != blank %}background-color: {{ section.settings.btn_color}};{% endif %}
      {% if section.settings.btn_text_color != blank %}color: {{ section.settings.btn_text_color}};{% endif %}
      {% if section.settings.btn_border_color != blank %}border-color: {{ section.settings.btn_border_color}};{% endif %}
    }
  {% endif %}

  {% if section.settings.text_color != blank %}
    {% if section.settings.text_color != 'rgba(0,0,0,0)'%}
      .product-item__meta,
      .product-item__footer {
        color: {{ section.settings.text_color }} !important;
      }
    {% else %}
      .product-item__meta,
      .product-item__footer {
        color: var(--color-primary)!important;
      }
    {% endif %}
  {% endif %}
{% endstyle %}

{% schema %}
{
  "name": "Collection",
  "tag": "section",
  "class": "shopify-section--collection",
  "settings": [
    {
      "type": "header",
      "content": "Display Settings"
    },
    {
      "type": "number",
      "id": "limit",
      "label": "Products per Page",
      "default": 9
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background Color"
    },
    {
      "type": "text",
      "id": "filter_trigger_text",
      "label": "Filter Trigger Text",
      "default": "Filter"
    },
    {
      "type": "select",
      "id": "filter_trigger_style",
      "label": "Filter Trigger Style",
      "options": [
        {
          "value": "filter-trigger--minimal",
          "label": "Icon Only"
        },
        {
          "value": "filter-trigger--text-icon",
          "label": "Text and Icon"
        }
      ],
      "default": "filter-trigger--minimal"
    },
    {
      "type": "select",
      "id": "paging",
      "label": "Collection Paging",
      "options": [
        {
          "value": "scroll",
          "label": "Endless Scroll"
        },
        {
          "value": "paginated",
          "label": "Paginated"
        }
      ],
      "default": "scroll"
    },
    {
      "type": "select",
      "id": "product_items_per_row",
      "label": "Product Items per Row (Desktop)",
      "options": [
        {
          "value": "md:grid-cols-2",
          "label": "2"
        },
        {
          "value": "md:grid-cols-3",
          "label": "3"
        },
        {
          "value": "md:grid-cols-4",
          "label": "4"
        }
      ],
      "default": "md:grid-cols-4"
    },
    {
      "type": "select",
      "id": "product_items_per_row_mobile",
      "label": "Product Items per Row (Mobile)",
      "options": [
        {
          "value": "grid-cols-1",
          "label": "1"
        },
        {
          "value": "grid-cols-2",
          "label": "2"
        }
      ],
      "default": "grid-cols-2"
    },
    {
      "type": "checkbox",
      "id": "show_title",
      "label": "Show Title",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_tools",
      "label": "Show Tools & Filters",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_badges",
      "label": "Show Badges",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_swatch",
      "label": "Show Swatches",
      "default": true
    },
    {
      "type": "header",
      "content": "Product Item Settings"
    },
    {
      "type": "text",
      "id": "image_override_term",
      "label": "Image Override",
      "info": "If this term is found in the file name, the image will become the main image on the product item."
    },
    {
      "type": "select",
      "id": "item_height",
      "label": "Grid Item Aspect Ratio",
      "options": [
        {
          "value": "",
          "label": "auto"
        },
        {
          "value": "9 | 11",
          "label": "9x11"
        },
        {
          "value": "6 | 8",
          "label": "6x8"
        },
        {
          "value": "1 | 1",
          "label": "1x1"
        }
      ],
      "default": ""
    },
    {
      "type": "select",
      "id": "image_display",
      "label": "Image Display",
      "options": [
        {
          "value": "bg-contain",
          "label": "Contain"
        },
        {
          "value": "bg-cover",
          "label": "Cover"
        }
      ],
      "default": "bg-contain"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color"
    },
    {
      "type": "header",
      "content": "Product Item Button Settings"
    },
    {
      "type": "color",
      "id": "btn_color",
      "label": "Button bg color"
    },
    {
      "type": "color",
      "id": "btn_text_color",
      "label": "Button text color"
    },
    {
      "type": "color",
      "id": "btn_border_color",
      "label": "Button border color"
    },
    {
      "type": "header",
      "content": "Filter Settings"
    },
    {
      "type": "textarea",
      "id": "sort",
      "label": "Sort Options",
      "default":"relavancy-asc:Relevance\nprice-asc:Lowest Price\nprice-desc:Highest Price"
    },
    {
      "type": "number",
      "id": "sort_width",
      "label": "Sort min-width",
      "default": 100
    },
    {
      "type": "text",
      "id": "exposed_filter_set",
      "label": "Exposed Filter Set",
      "default": "Category"
    },
    {
      "type": "select",
      "id": "exposed_filter_style",
      "label": "Exposed Filter Style",
      "options": [
        {
          "value": "buttons",
          "label": "Buttons"
        },
        {
          "value": "images",
          "label": "Images"
        }
      ],
      "default": "buttons"
    },
    {
      "type": "text",
      "id": "filter_exclusions",
      "label": "Filter Sidebar Set Exclusions"
    },
    {
      "type": "textarea",
      "id": "filter_sort_map",
      "label": "Filter Sort Map"
    },
    {
      "type": "select",
      "id": "filter_options_style_default",
      "label": "Filter Options Style Default",
      "options": [
        {
          "value": "list",
          "label": "List"
        },
        {
          "value": "buttons",
          "label": "Buttons"
        },
        {
          "value": "images",
          "label": "Images"
        }
      ],
      "default": "list"
    },
    {
      "type": "textarea",
      "id": "filter_options_style_map",
      "label": "Filter Options Style Map"
    },
    {
      "type": "textarea",
      "id": "filter_color_map",
      "label": "Filter Color Map"
    },
    {
      "type": "textarea",
      "id": "filter_image_map",
      "label": "Filter Image Map"
    },
    {
      "type": "textarea",
      "id": "filter_item_override_map",
      "label": "Filter Item Name Override Map",
      "info": "Each item on a new line with existing filter name and new filter name separated by \":\" example: \"Mother Denim : Mother\""
    },
    {
      "type": "header",
      "content": "Quick add"
    },
    {
      "type": "textarea",
      "id": "quick_add_options",
      "label": "Options for button label",
      "default": "size,amount",
      "info": "Only choose one depend on products."
    }
  ],
  "blocks": [
    {
      "type": "data-searchspring",
      "name": "SS Collection Data"
    },
    {
      "type": "data-shopify",
      "name": "Shopify Collection Data"
    },
    {
      "type": "data-remote",
      "name": "Open Remote Data",
      "limit": 1,
      "settings": [
        {
          "id": "remote_url",
          "label": "Remote URL",
          "type": "liquid"
        },
        {
          "type": "select",
          "id": "config_method",
          "label": "Request Method",
          "default": "GET",
          "options": [
            {
              "value": "GET",
              "label": "GET"
            },
            {
              "value": "POST",
              "label": "POST"
            },
            {
              "value": "PUT",
              "label": "PUT"
            }
          ]
        },
        {
          "type": "select",
          "id": "config_mode",
          "label": "CORS Request Mode",
          "default": "cors",
          "options": [
            {
              "value": "cors",
              "label": "cors"
            },
            {
              "value": "no-cors",
              "label": "no-cors"
            }
          ]
        },
        {
          "id": "config_headers",
          "label": "Request Headers",
          "type": "liquid"
        },
        {
          "id": "config_body",
          "label": "Request Body",
          "type": "liquid"
        },
        {
          "id": "map",
          "label": "Response Data Map",
          "type": "liquid"
        }
      ]
    },
    {
      "type":"set",
      "name":"Filter Set",
      "settings":[
        {
          "type":"text",
          "id":"title",
          "label":"Filter Set Title"
        },
        {
          "type":"select",
          "label":"Field",
          "id":"field",
          "default":"tags",
          "options":[
            {
              "label":"Tags",
              "value":"tags"
            },
            {
              "label":"Vendor",
              "value":"vendor"
            },
            {
              "label":"Product Type",
              "value":"type"
            }
          ]
        },
        {
          "type":"text",
          "label":"Product Type Restriction",
          "id":"product_type"
        },
        {
          "type":"text",
          "label":"Collection Inclusion",
          "id":"collection_inclusion",
          "info":"Only display on Collections as listed above in comma-separated handles"
        },
        {
          "type":"text",
          "label":"Collection Exclusion",
          "id":"collection_exclusion",
          "info":"Do not display on Collections listed above in comma-separated handles"
        },
        {
          "type":"select",
          "label":"Format",
          "id":"format",
          "options":[
            {
              "label":"List",
              "value":"list"
            },
            {
              "label":"Swatches",
              "value":"swatches"
            }
          ],
          "default":"list"
        },
        {
          "type":"text",
          "label":"Units",
          "id":"units"
        },
        {
          "type":"number",
          "label":"Minimum Values",
          "id":"minimum_values",
          "default":1
        },
        {
          "type":"header",
          "content":"Dynamic Filter Options"
        },
        {
          "type":"paragraph",
          "content":"Dynamic Filter Options require that the Field dropdown is set to `Tags`"
        },
        {
          "type":"text",
          "id":"inclusion",
          "label":"Tag Inclusion"
        },
        {
          "type":"text",
          "id":"delimiter",
          "label":"Tag Delimeter",
          "default":":"
        },
        {
          "type":"number",
          "id":"display",
          "label":"Display Segment"
        },
        {
          "type":"textarea",
          "id":"find_replace",
          "label":"Find and Replace",
          "info": "Add a new entry on a new line where the text to find and the text to replace is separated by \":\""
        },
        {
          "type":"textarea",
          "id":"sort_order",
          "label":"Sort Order",
          "info":"Values may include 'alpha', 'numeric', or a series of specific values one per line"
        },
        {
          "type":"header",
          "content":"Manual Filter Options"
        },
        {
          "type":"textarea",
          "id":"options",
          "label":"Filter Options",
          "info":"one option per line following format {{ label }}>>>{{ tag }}"
        },
        {
          "type":"text",
          "id":"remote_options",
          "label":"Remote Filter Options",
          "info":"Follows same formatting, but may be mapped to dynamic source per collection"
        }
      ]
    }
  ]
}
{% endschema %}
