[{"name": "theme_info", "theme_name": "Neptune 2.0", "theme_version": "2.0", "theme_author": "SLTWTR", "theme_documentation_url": "https://help.shopify.com/manual/online-store/themes/os20/themes-by-shopify/dawn", "theme_support_url": "https://sltwtr.com/"}, {"name": "Fonts", "settings": [{"type": "textarea", "id": "theme_fonts", "label": "Additional Fonts", "info": "To embed a font, copy the code and paste into this field."}]}, {"name": "t:settings_schema.colors.name", "settings": [{"type": "header", "content": "t:settings_schema.colors.settings.header__1.content"}, {"type": "color", "id": "colors_solid_button_labels", "default": "#FFFFFF", "label": "t:settings_schema.colors.settings.colors_solid_button_labels.label", "info": "t:settings_schema.colors.settings.colors_solid_button_labels.info"}, {"type": "color", "id": "colors_accent_1", "default": "#121212", "label": "t:settings_schema.colors.settings.colors_accent_1.label", "info": "t:settings_schema.colors.settings.colors_accent_1.info"}, {"type": "color", "id": "colors_accent_2", "default": "#334FB4", "label": "t:settings_schema.colors.settings.colors_accent_2.label"}, {"type": "header", "content": "t:settings_schema.colors.settings.header__2.content"}, {"type": "color", "id": "colors_text", "default": "#121212", "label": "t:settings_schema.colors.settings.colors_text.label", "info": "t:settings_schema.colors.settings.colors_text.info"}, {"type": "color", "id": "colors_outline_button_labels", "default": "#121212", "label": "t:settings_schema.colors.settings.colors_outline_button_labels.label", "info": "t:settings_schema.colors.settings.colors_outline_button_labels.info"}, {"type": "color", "id": "colors_background_1", "default": "#FFFFFF", "label": "t:settings_schema.colors.settings.colors_background_1.label"}, {"type": "color", "id": "colors_background_2", "default": "#F3F3F3", "label": "t:settings_schema.colors.settings.colors_background_2.label"}]}, {"name": "t:settings_schema.styles.name", "settings": [{"type": "header", "content": "t:settings_schema.styles.settings.header__1.content"}, {"type": "select", "id": "sale_badge_color_scheme", "options": [{"value": "accent-1", "label": "t:settings_schema.styles.settings.sale_badge_color_scheme.options__2.label"}, {"value": "accent-2", "label": "t:settings_schema.styles.settings.sale_badge_color_scheme.options__3.label"}, {"value": "background-2", "label": "t:settings_schema.styles.settings.sale_badge_color_scheme.options__1.label"}], "default": "accent-2", "label": "t:settings_schema.styles.settings.sale_badge_color_scheme.label"}, {"type": "select", "id": "sold_out_badge_color_scheme", "options": [{"value": "background-1", "label": "t:settings_schema.styles.settings.sold_out_badge_color_scheme.options__1.label"}, {"value": "inverse", "label": "t:settings_schema.styles.settings.sold_out_badge_color_scheme.options__2.label"}], "default": "inverse", "label": "t:settings_schema.styles.settings.sold_out_badge_color_scheme.label"}, {"type": "header", "content": "t:settings_schema.styles.settings.header__2.content"}, {"type": "select", "id": "accent_icons", "options": [{"value": "accent-1", "label": "t:settings_schema.styles.settings.accent_icons.options__1.label"}, {"value": "accent-2", "label": "t:settings_schema.styles.settings.accent_icons.options__2.label"}, {"value": "outline-button", "label": "t:settings_schema.styles.settings.accent_icons.options__3.label"}, {"value": "text", "label": "t:settings_schema.styles.settings.accent_icons.options__4.label"}], "default": "text", "label": "t:settings_schema.styles.settings.accent_icons.label"}]}, {"name": "t:settings_schema.layout.name", "settings": [{"type": "select", "id": "page_width", "options": [{"value": "1200", "label": "t:settings_schema.layout.settings.page_width.options__1.label"}, {"value": "1600", "label": "t:settings_schema.layout.settings.page_width.options__2.label"}], "default": "1600", "label": "t:settings_schema.layout.settings.page_width.label"}]}, {"name": "Social media", "settings": [{"type": "header", "content": "Sharing links"}, {"type": "text", "id": "social_facebook_link", "label": "Facebook link"}, {"type": "text", "id": "social_instagram_link", "label": "Instagram link"}, {"type": "text", "id": "social_pinterest_link", "label": "Pinterest link"}, {"type": "text", "id": "social_twitter_link", "label": "Twitter link"}, {"type": "text", "id": "social_youtube_link", "label": "Youtube link"}, {"type": "text", "id": "social_snapchat_link", "label": "Snapchat link"}, {"type": "text", "id": "social_tiktok_link", "label": "TikTok link"}]}, {"name": "t:settings_schema.search_input.name", "settings": [{"type": "header", "content": "t:settings_schema.search_input.settings.header.content"}, {"type": "checkbox", "id": "predictive_search_enabled", "default": true, "label": "t:settings_schema.search_input.settings.predictive_search_enabled.label"}, {"type": "checkbox", "id": "predictive_search_show_vendor", "default": false, "label": "t:settings_schema.search_input.settings.predictive_search_show_vendor.label", "info": "t:settings_schema.search_input.settings.predictive_search_show_vendor.info"}, {"type": "checkbox", "id": "predictive_search_show_price", "default": true, "label": "t:settings_schema.search_input.settings.predictive_search_show_price.label", "info": "t:settings_schema.search_input.settings.predictive_search_show_price.info"}]}, {"name": "t:settings_schema.favicon.name", "settings": [{"type": "image_picker", "id": "favicon", "label": "t:settings_schema.favicon.settings.favicon.label", "info": "t:settings_schema.favicon.settings.favicon.info"}]}, {"name": "t:settings_schema.currency_format.name", "settings": [{"type": "header", "content": "t:settings_schema.currency_format.settings.content"}, {"type": "paragraph", "content": "t:settings_schema.currency_format.settings.paragraph"}, {"type": "checkbox", "id": "currency_code_enabled", "label": "t:settings_schema.currency_format.settings.currency_code_enabled.label", "default": true}]}, {"name": "Code", "settings": [{"type": "header", "content": "Code Injections"}, {"type": "liquid", "id": "code_head_start", "label": "Start of head"}, {"type": "liquid", "id": "code_head_end", "label": "End of head"}, {"type": "liquid", "id": "code_body_start", "label": "Start of body"}, {"type": "liquid", "id": "code_body_end", "label": "End of body"}]}, {"name": "Product Item", "settings": [{"type": "checkbox", "label": "Enable video as default media", "id": "product_item_enable_video_first", "default": false}]}, {"name": "Inventory", "settings": [{"type": "number", "label": "Variant Low Stock Threshold", "id": "variant_lowstock_threshold", "info": "Low Stock Inventory threshold for flagging.", "default": 3}, {"type": "text", "label": "PDP Low Stock Message", "id": "variant_lowstock_message", "info": "PDP message to display in Add to Cart Button. To add product qty in the text, insert the following: **n**", "default": "Only **n** Left, <PERSON><PERSON>!"}, {"type": "text", "label": "Colection Item Low Stock Message", "id": "collection_lowstock_message", "info": "Flag for Collection Grid items if below inventory quantity in user's size. To add product qty in the text, insert the following: **n**, so add the user's size in the message, use: **size**", "default": "**n** Left in **size**"}]}, {"name": "Breadcrumbs", "settings": [{"type": "link_list", "label": "Breadcrumb Path Menu", "id": "breadcrumbs"}]}, {"name": "Sections", "settings": [{"type": "number", "label": "Asynchronous section offset", "id": "async_offset", "default": 0}]}, {"name": "SEO", "settings": [{"type": "checkbox", "id": "show_meta", "label": "Show meta title and description", "default": false}]}, {"name": "Free Shipping CTA", "settings": [{"type": "checkbox", "id": "free_shipping_enable", "label": "Enable", "default": false}, {"type": "paragraph", "content": "For each country, use the 2 character country code or the currency code. Each country annoucement bar must be separated by a line break and in the follow format: AU | Free Shipping on orders over $50"}, {"type": "textarea", "id": "free_shipping_values", "label": "Value", "info": "Numbers only", "default": "US | 50"}, {"type": "textarea", "id": "free_shipping_message", "label": "Message Before Free Shipping", "default": "US | You're $50 away from free shipping."}, {"type": "textarea", "id": "free_shipping_message_success", "label": "Message After Free Shipping", "default": "US | Yay! You got Free Shipping."}]}, {"name": "Persistent Messaging", "settings": [{"type": "color", "id": "persistent_messaging_color", "label": "Persistent Message Color", "default": "#545454"}, {"label": "Persistent Message Font Weight", "type": "select", "id": "persistent_messaging_weight", "default": "normal", "options": [{"value": "normal", "label": "Normal"}, {"value": "bold", "label": "Bold"}]}]}, {"name": "SearchSpring", "settings": [{"type": "checkbox", "id": "ss_enable", "label": "Enable SearchSpring", "default": true}, {"type": "text", "id": "ss_site_id", "label": "Site ID", "default": "00svms"}, {"type": "checkbox", "id": "ss_enable_logs", "label": "Enable Beacon API Logs", "default": false}]}, {"name": "International Logic (Global E)", "settings": [{"type": "checkbox", "id": "international_logic", "label": "Enable International Logic", "default": true}, {"type": "text", "id": "international_hide", "label": "Hide elements", "info": "Comma-separated valid query selectors of elements to hide if the user has a international-localized experience"}, {"type": "text", "id": "international_redirect", "label": "Page Redirects", "info": "Comma-separated page handles to redirect to Home if the user has a international-localized experience"}, {"type": "text", "id": "international_tag_redirect", "label": "Product Tag Exclusions", "info": "Comma-separated product tags to be excluded from search + collections and redirect to Home if the user has a international-localized experience"}, {"type": "textarea", "id": "duty_terms_messages", "label": "Country Specific Duty Terms Checkout Messaging", "info": "Each country duty terms message must be separated by a line break and in the follow format: AU | Annoucement Text"}]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": [{"type": "text", "label": "Klaviyo Public API Key", "id": "klaviyo_api_key", "default": "SqytQQ"}, {"type": "header", "content": "Back in stock"}, {"type": "text", "label": "Klaviyo list id", "id": "klaviyo_list"}, {"type": "checkbox", "label": "Back in Stock", "id": "back_in_stock", "default": false}]}, {"name": "Accessibe", "settings": [{"type": "checkbox", "label": "Enable Accessibe", "id": "accessibe", "default": false}]}, {"name": "Enhanced Pricing", "settings": [{"type": "text", "label": "Shopify Storefront API access token", "id": "storefront_api_access_token"}]}, {"name": "Accounts Page Required <PERSON><PERSON>", "settings": [{"type": "text", "label": "Input Field Required Copy", "id": "required_error", "default": "* Indicates Required Field"}]}, {"name": "OneTrust", "settings": [{"type": "checkbox", "label": "Enable OneTrust", "id": "onetrust_enable", "default": true}, {"type": "liquid", "label": "<PERSON><PERSON>", "id": "onetrust_script"}]}, {"name": "Development", "settings": [{"type": "checkbox", "id": "hide_preview_bar", "label": "Hide Preview Bar", "info": "Do not display priview bar iframe from Shopify on bottom of screen", "default": false}, {"type": "checkbox", "id": "hide_development", "label": "Hide includes for dev", "info": "This will hide any specific includes per specific theme.", "default": false}]}]