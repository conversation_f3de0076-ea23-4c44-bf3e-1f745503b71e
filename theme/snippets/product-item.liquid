{% raw %}
{%- assign show_swatch = '{% endraw %}{{ show_swatch }}{% raw %}'-%}
{% assign product_tags = product.tags | replace: '"', '`' %}
{% assign product_video_first = {% endraw %}{{ settings.product_item_enable_video_first }}{% raw %} %}
<article
  class="product-item__wrapper flex flex-col font-body relative {% endraw %}{{ classes }} {{ class }}{% raw %}"
  data-product-item
  data-position="{{ product.page }},{{forloop.index0}}"
  data-product-id="{{ product.id }}"
  data-product="{
                  `id`: {{ product.id }},
                  `title`: `{{ product.title }}`,
                  `handle`: `{{ product.handle }}`,
                  `sku`: `{{ product.variants[0].sku }}`,
                  `price`: {{ product.price }},
                  `compare_at_price`: {{ product.compare_at_price }},
                  `featured_image`: `{{ product.featured_image | split: '?' | first }}`,
                  `thumbnail_image`: `{{ product.thumbnail_image | split: '?' | first }}`
                }"
  data-tags="{{ product_tags }}"
>

  <div class="relative flex flex-col h-full product-item p-{{ product.id }}" data-product-item-content>

    <div
      class="flex flex-col h-full overflow-hidden product-item__outer group"
      hover
      neptune-engage='{
        on:keyup
        targets: [
          {
            selector:.product-item__outer,
            classes:remove:active
          },
          {
            selector:_self,
            classes:add:active
          }
        ]
      }'
      aria-label="Product details for {{ product.title }}"
      >
      <div class="product-item__contents relative flex-grow rounded-sm {% if forloop.length == 1%}unique-product{% endif %}">
        <a
          id="product_link_{{ product.id }}"
          class="product-item__link"
          role="link"
          onclick="Neptune.later.store(event, 'product_item_click')"
          {{'h'}}ref="{% endraw %}{% if collection != blank %}/collections/{% if collection.handle %}{{ collection.handle }}{% else %}{{ collection }}{% endif %}{% endif %}{% raw %}/products/{{ product.handle }}"
        >
          <div class="relative product-item__image-bg group">
            {% endraw %}{% if request.page_type == 'collection' or request.page_type == 'search' %}{% raw %}
              <div class="product-gallery product-gallery-slider swiper-container w-full"
                role="region"
                aria-roledescription="carousel"
                aria-live="polite"
                aria-label="Product gallery slider"
                neptune-swiper="
                  {
                    zoom: true,
                    cssMode: true,
                    slidesPerView: 1,
                    pagination: {'el': '#product_link_{{ product.id }} .swiper-pagination','type': 'bullets'},
                    breakpoints: {
                      1024: {
                        enabled: false
                      }
                    }
                  }
                "
                tabindex="0"
              >
                <figure class="product-item__image-wrapper swiper-wrapper" data-product-item-image onscroll="Neptune.uncomment.uncomment(_n.qs('[neptune-uncomment]', this)); if(this.scrollLeft < this.clientWidth/2){ _n.qs('.pagination',this).classList.add('start') } else { _n.qs('.pagination',this).classList.remove('start') }">

                  <div class="swiper-slide product-item__image">
                    {%- assign alt_image_term = '{% endraw %}{{ alt_image_term }}{% raw %}'-%}
                    {%- if alt_image_term != '' -%}
                      {%- for image in product.images -%}
                        {%- assign downcase_image = image | downcase -%}
                        {%- assign downcase_term = alt_image_term | downcase -%}

                        {%- if downcase_image contains downcase_term -%}
                          {%- assign alt_image = image -%}
                        {%- endif -%}
                      {%- endfor -%}
                    {%- endif -%}
                    <img
                      class="object-contain"
                      aria-label="Image of product {{ product.title }}"
                      loading="lazy"
                      alt="{{ product.title }} - {{ product.images[0].alt }}"
                      {{'s'}}rc="{% if alt_image != blank %}{{ alt_image | append: '&width=700' }}{% else %}{{ product.featured_image | append: '&width=700' }}{% endif %}"
                      id="img_{{ product.id }}"
                    />

										{% endraw %}
											<div class="product-item-badge__container group-hover:lg:opacity-0">
												{% render 'item-image-badge' excluded_item_badge:excluded_item_badge %}
											</div>
										{% raw %}
                  </div>

                  {% if product.hover_image != blank or product.hover_video contains 'https' %}
                    <div 
                      class="swiper-slide product-item__image lg:!absolute lg:transition-opacity lg:opacity-0 lg:group-hover:opacity-100"
                      neptune-uncomment="mouseenter"
                      aria-hidden="true"
                      tabindex="0"
                      onfocus="Neptune.uncomment.uncomment(this)"
                    >

                      {% if product.hover_video contains 'https' %}
                        <!--<video class="product-video object-contain w-full h-full" preload="true" {{'s'}}rc="{{ product.hover_video }}" muted mute autoplay playsinline loop loading="lazy" aria-label="Video of {{ product.title }}"><source {{'s'}}rc="{{ product.hover_video }}"></video>-->
                      {% else %}
                        <img
                          class="object-contain w-full h-full"
                          {{'s'}}rc="{{ product.hover_image | append: '&width=700' }}"
                          loading="lazy"
                          alt="Hover image of {{ product.title }}"
                        />
                      {% endif %}
                    </div>
                  {% endif %}

                </figure>

                <div class="product__pagination lg:hidden" role="navigation" aria-label="Product gallery navigation">
                  <nav class="swiper-pagination product-gallery__pagination" aria-label="Paging controls"></nav>
                </div>
              </div>
            {% endraw %}{% else %}{% raw %}
              <figure class="product-item__image-wrapper" data-product-item-image>
                <div class="product-item__image">
                  <img
                    class="product-item__image--main"
                    aria-label=""
                    loading="lazy"
                    alt="{{ product.title }} - {{ product.images[0].alt }}"
                    {{'s'}}rc="{{ product.featured_image }}"
                    id="img_{{ product.id }}"
                  />
                </div>

                {% if product.hover_image != blank %}
                  <div class="product-image__hover absolute w-full h-full transition-opacity opacity-0 group-hover:opacity-100" neptune-uncomment="mouseenter">

                    {% if product.hover_video contains 'https' %}
                      <!--<video class="object-contain w-full h-full product-video" preload="true" {{'s'}}rc="{{ product.hover_video }}" muted mute autoplay loop aria-label="Video of {{ product.title }}"><source {{'s'}}rc="{{ product.hover_video }}"></video>-->
                    {% else %}
                      <img
                      class="object-contain w-full h-full"
                      {{'s'}}rc="{{ product.hover_image }}"
                      loading="lazy"
                      alt="Hover image of {{ product.title }}"
                      />
                    {% endif %}
                  </div>
                {% endif %}

              </figure>
            {% endraw %}{% endif %}{% raw %}
          </div>

          <div class="product-item__meta">

            <div class="product-item__title--meta">
              <h3 class="my-1 leading-snug product-title product-item__title">
                {{ product.title | split: ' - ' | first }}
              </h3>
              {% if product.title contains ' - ' %}
              <h4 class="product-item__subtitle">
                {{ product.title | split: ' - ' | last }}
              </h4>
              {% endif %}
            </div>

            <div class="product-item__price flex">
              {% endraw %}
              {% render 'price-removal' %}
              {% raw %}
              {% if product.compare_at_price > product.price %}
                <s>{{ product.compare_at_price | money: 'local' | remove: removal }}</s>
              {% endif %}
              <span
                class="global-price"
                global-selector="prices.item.label"
                global-default="{{ product.price | money: 'local' | remove: removal }}"
                global-variant="{{ product.selected_or_first_available_variant.id }}"
              >
                {{ product.price | money: 'local' | remove: removal }}
              </span>
            </div>
            {% if show_swatch == 'true' %}
              {%- assign count = 0 -%}
              {% assign totalSiblings = product.siblings | size %}
              {% comment %}Sibling data is filtered in JavaScript via the sibling-data-sync component{% endcomment %}
              <div class="product-item__siblings product-siblings gap-2.5">
                {% assign product_grid_index = forloop.index0 %}

                {% if totalSiblings > 1 %}
                  {% for sibling in product.siblings %}

                    {% assign color = '' %}
                    {% for tag in sibling.tags %}
                      {% assign tag_handle = tag | handleize %}
                      {% if tag_handle contains 'Color:' %}
                        {% assign color = tag_handle | remove: 'Color:' | strip | downcase %}
                        {% break %}
                      {% endif %}
                    {% endfor %}

                    {% if forloop.index <= 4 %}

                      {% if sibling.swatch_image != blank %}
                        {% assign swatchImage = sibling.swatch_image %}
                      {% else %}
                        {% assign swatchImage = sibling.featured_image | append: '&crop=region&crop_height=32&crop_left=831&crop_top=1187&crop_width=64' %}
                      {% endif %}

                      <button
                      class="{%- if sibling.handle == product.handle -%}product-siblings__swatch active{% else %}product-siblings__swatch{%- endif -%} {{ color |  prepend: ' '  }}"
                      onclick="event.preventDefault();
                      event.stopPropagation();
                      let p = collection.products[{{ product_grid_index }}].siblings[{{ forloop.index0 }}];
                      let genderP = Gender.setItemImages(p) || p;
                      // Preserve the filtered siblings array from the current product
                      const filteredSiblings = collection.products[{{ product_grid_index }}].siblings;
                      collection.products[{{ product_grid_index }}] = genderP;
                      // Restore the filtered siblings array to the new product
                      collection.products[{{ product_grid_index }}].siblings = filteredSiblings;
                      Neptune.liquid.load('Collection'); "
                      >
                        <img {{'s'}}rc="{{ swatchImage }}" alt="Swatch for {{ sibling.title }}" loading="lazy" width="400" height="533" class="w-full h-full object-cover">
                      </button>
                    {% else %}
                      {%- assign count = count | plus: 1 -%}
                    {% endif %}
                  {% endfor %}
                  {% unless count == 0 %}
                    <span class="text-[8px]">+{{ count }}</span>
                  {% endunless %}
                {% else %}
                  <button class="product-siblings__swatch active">
                    {% if product.swatch_image != blank %}
                      {% assign swatchImage = product.swatch_image %}
                    {% else %}
                      {% assign swatchImage = product.featured_image | append: '&crop=region&crop_height=32&crop_left=831&crop_top=1187&crop_width=64' %}
                    {% endif %}
                    <img {{'s'}}rc="{{ swatchImage }}" alt="Active swatch for {{ product.title }}" loading="lazy" width="400" height="533" class="w-full h-full object-cover">
                  </button>
                {% endif %}

              </div>
            {% endif %}
          </div>
        </a>

        {% assign product_tags = product.tags | replace: '"', "`" %}
        <div class="product-item__footer {% endraw %}{% if action != blank %}product-item__footer--{{ action }}{% endif %}{% raw %} flex flex-wrap justify-between items-start" data-tags="{{ product_tags }}">

          {% endraw %}{% if action != blank %}{% raw %}
            <button
             data-handle="{{ product.handle }}"
             onclick="QuickAdd(this.dataset.handle);this.classList.add('gtmQuickAddClicked');"
             class="quick-add__button quick-add__button--action button button--light flex items-center hidden quick-1">
              <span>{% endraw %}{{ 'products.product.quick_add.add_to_cart' | t }}{% raw %}</span>
              <span class="quick-add__button--plus">{% endraw %}{% render 'icon', icon:'plus', width:12, height:12 %}{% raw %}</span>
              <span class="quick-add__button--minus">{% endraw %}{% render 'icon', icon:'minus', width:12, height:12 %}{% raw %}</span>
            </button>


          {% if product.variants.size > 1 %}

            {% endraw %}{% if action == 'quick-add-toggle' %}{% raw %}

            {% comment%}This button is for the quick add toggle experience, as seen on the PLP product item{% endcomment%}

            <button
             data-handle="{{ product.handle }}"
             onclick="this.classList.toggle('active');this.classList.add('gtmQuickAddClicked');"
             class="add-to-cart__quick-add-toggle quick-add__button quick-add--toggle button button--light product-item__quick-add-toggle hidden lg:flex items-center quick-2 order-1">
              <span>{% endraw %}{{ 'products.product.quick_add.add_to_cart' | t }}{% raw %}</span>
              <span class="quick-add__button--plus">{% endraw %}{% render 'icon', icon:'plus', width:12, height:12 %}{% raw %}</span>
              <span class="quick-add__button--minus">{% endraw %}{% render 'icon', icon:'minus', width:12, height:12 %}{% raw %}</span>
            </button>

            {% endraw %}{% endif %}{% raw %}

            <div class="product-item__container order-3 w-full">
              <div class="product-item__variants w-full">
                <div class="option-selection quick-add__form--wrapper">
                  <script>
                    window['quickAddProduct_{{ product.handle | handle }}'] = {
                      images: {{ product.images | json }},
                      alt_images: {{ product.alt_images | json }}
                    };
                  </script>
                  {% for variant in product.variants %}
                    <div class="quick-add__form--option-container relative">
                      {% if variant.inventory_quantity > 0 or variant.inventory_quantity == blank %}
                        {% assign _compare_at_price = variant.compare_at_price |  default: variant.price %}
                        <input id="{{ product.handle }}-variant-{{ variant.title | handle }}" data-variants="" data-available="{% if variant.inventory_quantity == blank %}empty{% else %}{{ variant.inventory_quantity }}{% endif %}" type="radio" name="option2" value="{{ variant.id }}" class="absolute inset-0 w-full h-full opacity-0 cursor-pointer" onclick="this.classList.add('gtmVariantClicked');" tabindex="-1" onchange="
                        if(this.checked){
                          {% endraw %}{% if action == 'quick-add-toggle'%}{% raw %}
                            const tags = `{{ product_tags }}`;
                            const itemToAdd = {
                              id: {{ variant.id }},
                              quantity: 1,
                              properties: {
                                _compare_at_price: {{ _compare_at_price | remove: '.' | default: 0 }},
                                _tags: tags ? tags.split(',').map(tag => tag.trim()) : [],
                              }
                            };
                            Neptune.cart.add(itemToAdd);BackInStock.show(this);
                          {% endraw %}{% endif %}{% raw %}
                          {% endraw %}{% if action == 'variant-selection'%}_n.cousin(this,'.product-item','.product-item__add-to-cart').disabled=false; _n.cousin(this,'.product-item','.product-item__add-to-cart').innerHTML = '{{ 'products.product.quick_add.add_to_cart_variants_exposed' | t }}'; this.closest('.product-item__container').querySelector('.back-in-stock-container').classList.add('hidden');{% endif %}{% raw %}
                        }">
                      {% else %}
                          <input id="{{ product.handle }}-variant-{{ variant.title | handle }}" data-variant="{{ variant.id }}" data-variants="" data-available="{{ variant.inventory_quantity }}" type="radio" name="option2" value="{{ variant.id }}" class="absolute inset-0 w-full h-full opacity-0 cursor-pointer" {% endraw %}{% if product.tags contains 'exclude_BIS' %}disabled{% else %}onchange="if(this.checked){BackInStock.show(this)}; Neptune.later.store(event, 'quick_add_click')"{% endif %}{% raw %}>
                      {% endif %}

                      <label class="{% endraw %}{{ action | handle }}{% raw %} quick-add__form--option cursor-pointer swatch--nested" for="{{ product.handle }}-variant-{{ variant.title | handle }}" title="{{ variant.option2 }}" data-available="{{ variant.inventory_quantity }}" tabindex="0"
                        onkeydown="if(event.key === 'Enter' || event.key === ' '){
                        event.preventDefault();
                        const radio = document.getElementById('{{ product.handle }}-variant-{{ variant.title | handle }}');
                        radio.checked = true;
                        radio.dispatchEvent(new Event('change'));
                        }"
                      >
                          <span class="checkbox-controlled-bold">{{ variant.option2 }}</span>
                        </label>
                    </div>
                  {% endfor %}

                </div>
              </div>

              {% unless product.tags contains 'exclude_BIS' %}
                {% endraw %}
                    {% render 'back-in-stock' product:product %}
                {% raw %}
              {% endunless %}
            </div>
            {% endraw %}


            {% if action=='variant-selection'%}{% raw %}

            {% comment%}This button is for product items with variants exposed, as seen on the PDP Shop the Look{% endcomment%}

            <button
             disabled
             data-handle="{{ product.handle }}"
             onclick="
                const actualVariant = _n.qs('input:checked',this.previousElementSibling);
                const tags = `{{ product_tags }}`;
                const itemToAdd = {
                  id: actualVariant.value,
                  quantity: 1,
                  properties: {
                    _compare_at_price: {{ _compare_at_price | remove: '.' | default: 0 }},
                    _tags: tags ? tags.split(',').map(tag => tag.trim()) : [],
                  }
                };
             Neptune.cart.add(itemToAdd); Neptune.later.store(event, 'quick_add_click')"
             class="{% if cart.attributes['segment__test__shop-the-look'] == 'new' %}w-full order-4{% endif %} add-to-cart__variant-selection button button--light product-item__add-to-cart hidden lg:flex items-center">
              <span>
                {% endraw %}
                  {% assign custom_class = __settings.custom_class %}
                  {% if custom_class contains 'complete-the-look' %}
                    {{ 'products.product.quick_add.select_size' | t }}
                  {% else %}
                    {{ 'products.product.quick_add.add_to_cart_variants_exposed' | t }}
                  {% endif %}
                {% raw %}
              </span>
            </button>

            {% endraw %}{% endif %}

            {% raw %}

          {% else %}

            {% comment%}This button is for product items without variants, as seen on socks or bags on the PLP{% endcomment%}
            {% assign variant = product.variants[0] %}
            {% if variant.availability == 'InStock' %}
              {% assign product_available = true %}
            {% elsif variant.inventory_quantity != blank and variant.inventory_quantity > 0 %}
              {% assign product_available = true %}
            {% else %}
              {% assign product_available = false %}
            {% endif %}

            {% if product.tags contains 'exclude_BIS' %}
              <p class="text-sm">Product out of stock</p>
            {% else %}
              <button
              onclick="{% if product_available %}
                const tags = `{{ product_tags }}`;
                const itemToAdd = {
                  id: {{ variant.id }},
                  quantity: 1,
                  properties: {
                    _compare_at_price: {{ variant.compare_at_price | default: 0 | remove: '.' }},
                    _tags: tags ? tags.split(',').map(tag => tag.trim()) : [],
                  }
                };
              Neptune.cart.add(itemToAdd);
              this.classList.add('gtmQuickAddClicked'); Neptune.later.store(event, 'quick_add_click'); Neptune.later.store(event, 'quick_add_click');{% else %}this.classList.toggle('active'); this.nextElementSibling.querySelector('.back-in-stock-container').classList.toggle('hidden');{% endif %}"
              class="add-to-cart__no-variants quick-add__button quick-add--toggle b-3 button button--light product-item__add-to-cart product-item__quick-add-toggle hidden lg:flex items-center order-1 mb-0">
                <span>{% endraw %}{{ 'products.product.quick_add.optional_add_to_cart' | t }}{% raw %}</span>
                <span class="quick-add__button--plus">{% endraw %}{% render 'icon', icon:'plus', width:12, height:12 %}{% raw %}</span>
                <span class="quick-add__button--minus">{% endraw %}{% render 'icon', icon:'minus', width:12, height:12 %}{% raw %}</span>
              </button>
              <div class="product-item__container order-2 w-full">
                {% unless product.tags contains 'exclude_BIS' %}
                  {% endraw %}
                      {% render 'back-in-stock' product:product %}
                  {% raw %}
                {% endunless %}
              </div>
            {% endif %}

          {% endif %}

          {% endraw %}{% endif %}{% raw %}
            <div class="product-item__badges order-2 empty:hidden">
              {% endraw %}{% render 'item-badge' %}{% raw %}
            </div>
           {% endraw %}
           {% if details_link %}
              <a href="/products/{% raw %}{{ product.handle }}{% endraw %}" class="product-item__details-link order-4 block text-center w-full" onclick="Neptune.later.store(event, 'product_item_click')">{{ 'products.product.view_details' | t }}</a>
            {% endif %}
          {% raw %}
        </div>

      </div>
    </div>

  </div>
</article>

{% endraw %}