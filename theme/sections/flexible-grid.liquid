{% schema %}
{
  "name": "Flexible Grid",
  "settings": [
    {
      "type": "header",
      "content": "A/B Testing"
    },
    {
      "type": "checkbox",
      "id": "remove_section_condition",
      "label": "Remove Section",
      "default": false,
      "info": "This will hide the section in one of the experiment variations."
    },
    {
      "type": "header",
      "content": "Display Settings"
    },
    {
      "type": "checkbox",
      "id": "async",
      "label": "Asynchronously Load Section on Scroll",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_desktop",
      "label": "Show desktop",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_mobile",
      "label": "Show mobile",
      "default": true
    },
    {
      "type": "header",
      "content": "Section Display Settings"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color"
    },
    {
      "type": "select",
      "id": "container",
      "label": "Container",
      "options": [
        {
          "value": "container",
          "label": "Container"
        },
        {
          "value": "w-full",
          "label": "Full width"
        }
      ],
      "default": "w-full"
    },
    {
      "type": "select",
      "id": "height",
      "label": "Height (Desktop)",
      "options": [
        {
          "value": "lg:h-100v",
          "label": "Full height"
        },
        {
          "value": "lg:h-70v",
          "label": "Three quarter height"
        },
        {
          "value": "lg:h-50v",
          "label": "Half height"
        },
        {
          "value": "lg:h-20v",
          "label": "Quarter height"
        },
        {
          "value": "lg:h-auto content-height",
          "label": "Text content height"
        },
        {
          "value": "lg:h-auto image-height",
          "label": "Image height"
        },
        {
          "value": "lg:h-auto content-below-height",
          "label": "Content outside image"
        }
      ],
      "default": "lg:h-100v"
    },
    {
      "type": "select",
      "id": "height_mobile",
      "label": "Height (Mobile)",
      "options": [
        {
          "value": "h-100v",
          "label": "Full height"
        },
        {
          "value": "h-70v",
          "label": "Three quarter height"
        },
        {
          "value": "h-50v",
          "label": "Half height"
        },
        {
          "value": "h-20v",
          "label": "Quarter height"
        },
        {
          "value": "h-auto content-height-mobile",
          "label": "Text content height"
        },
        {
          "value": "h-auto image-height-mobile",
          "label": "Image height"
        },
        {
          "value": "h-auto content-below-height",
          "label": "Content outside image"
        }
      ],
      "default": "h-100v"
    },
    {
        "type": "checkbox",
        "id": "reverse_content",
        "label": "Reverse columns",
        "default": false,
        "info": "In case the text is not inside the image and you want to invert the order of the column, check this option to be true."
    },
    {
      "type": "select",
      "id": "item_height",
      "label": "Grid Item Aspect Ratio (Desktop)",
      "info": "To use Grid Item Aspect Ratio height, set section height for desktop and mobile to text content height.",
      "options": [
        {
          "value": "",
          "label": "auto"
        },
        {
          "value": "lg:aspect-w-16 lg:aspect-h-9",
          "label": "16x9"
        },
        {
          "value": "lg:aspect-w-9 lg:aspect-h-16",
          "label": "9x16"
        },
        {
          "value": "lg:aspect-w-4 lg:aspect-h-3",
          "label": "4x3"
        },
        {
          "value": "lg:aspect-w-3 lg:aspect-h-4",
          "label": "3x4"
        },
        {
          "value": "lg:aspect-w-6 lg:aspect-h-4",
          "label": "6x4"
        },
        {
          "value": "lg:aspect-w-4 lg:aspect-h-6",
          "label": "4x6"
        },
        {
          "value": "lg:aspect-w-8 lg:aspect-h-5",
          "label": "8x5"
        },
        {
          "value": "lg:aspect-w-5 lg:aspect-h-8",
          "label": "5x8"
        },
        {
          "value": "lg:aspect-w-7 lg:aspect-h-5",
          "label": "7x5"
        },
        {
          "value": "lg:aspect-w-5 lg:aspect-h-7",
          "label": "5x7"
        },
        {
          "value": "lg:aspect-w-1 lg:aspect-h-1",
          "label": "1x1"
        }
      ],
      "default": ""
    },
    {
      "type": "select",
      "id": "item_height_mobile",
      "label": "Grid Item Aspect Ratio (Mobile)",
      "info": "To use Grid Item Aspect Ratio height, set section height for desktop and mobile to text content height. If you set for mobile, this will also default for desktop.",
      "options": [
        {
          "value": "",
          "label": "auto"
        },
        {
          "value": "aspect-w-16 aspect-h-9",
          "label": "16x9"
        },
        {
          "value": "aspect-w-9 aspect-h-16",
          "label": "9x16"
        },
        {
          "value": "aspect-w-4 aspect-h-3",
          "label": "4x3"
        },
        {
          "value": "aspect-w-3 aspect-h-4",
          "label": "3x4"
        },
        {
          "value": "aspect-w-6 aspect-h-4",
          "label": "6x4"
        },
        {
          "value": "aspect-w-4 aspect-h-6",
          "label": "4x6"
        },
        {
          "value": "aspect-w-8 aspect-h-5",
          "label": "8x5"
        },
        {
          "value": "aspect-w-5 aspect-h-8",
          "label": "5x8"
        },
        {
          "value": "aspect-w-7 aspect-h-5",
          "label": "7x5"
        },
        {
          "value": "aspect-w-5 aspect-h-7",
          "label": "5x7"
        },
        {
          "value": "aspect-w-1 aspect-h-1",
          "label": "1x1"
        }
      ],
      "default": ""
    },
    {
      "type": "checkbox",
      "id": "sticky",
      "label": "Sticky layout",
      "default": false
    },
    {
      "type": "header",
      "content": "Gutter Settings"
    },
    {
      "type": "select",
      "id": "block_gutters",
      "label": "Block gutters (Desktop)",
      "options": [
        {
          "value": "lg:px-0",
          "label": "None"
        },
        {
          "value": "lg:px-1",
          "label": ".25rem"
        },
        {
          "value": "lg:px-2",
          "label": ".5rem"
        },
        {
          "value": "lg:px-4",
          "label": "1rem"
        },
        {
          "value": "lg:px-8",
          "label": "2rem"
        },
        {
          "value": "lg:px-16",
          "label": "4rem"
        },
        {
          "value": "lg:px-32",
          "label": "8rem"
        },
        {
          "value": "lg:px-64",
          "label": "16rem"
        }
      ],
      "default": "lg:px-0"
    },
    {
      "type": "select",
      "id": "block_gutters_mobile",
      "label": "Block gutters (Mobile)",
      "options": [
        {
          "value": "p-0",
          "label": "None"
        },
        {
          "value": "p-1",
          "label": ".25rem"
        },
        {
          "value": "p-2",
          "label": ".5rem"
        },
        {
          "value": "p-4",
          "label": "1rem"
        },
        {
          "value": "p-8",
          "label": "2rem"
        },
        {
          "value": "p-16",
          "label": "4rem"
        },
        {
          "value": "p-32",
          "label": "8rem"
        },
        {
          "value": "p-64",
          "label": "16rem"
        }
      ],
      "default": "p-0"
    },
    {
      "type": "header",
      "content": "Section Spacing Settings (Desktop)"
    },
    {
      "type": "select",
      "id": "section_top_margin",
      "label": "Margin top",
      "options": [
        {
          "value": "lg:mt-0",
          "label": "None"
        },
        {
          "value": "lg:mt-1",
          "label": ".25rem"
        },
        {
          "value": "lg:mt-2",
          "label": ".5rem"
        },
        {
          "value": "lg:mt-4",
          "label": "1rem"
        },
        {
          "value": "lg:mt-8",
          "label": "2rem"
        },
        {
          "value": "lg:mt-16",
          "label": "4rem"
        },
        {
          "value": "lg:mt-32",
          "label": "8rem"
        },
        {
          "value": "lg:mt-64",
          "label": "16rem"
        }
      ],
      "default": "lg:mt-0"
    },
    {
      "type": "select",
      "id": "section_bottom_margin",
      "label": "Margin bottom ",
      "options": [
        {
          "value": "lg:mb-0",
          "label": "None"
        },
        {
          "value": "lg:mb-1",
          "label": ".25rem"
        },
        {
          "value": "lg:mb-2",
          "label": ".5rem"
        },
        {
          "value": "lg:mb-4",
          "label": "1rem"
        },
        {
          "value": "lg:mb-8",
          "label": "2rem"
        },
        {
          "value": "lg:mb-16",
          "label": "4rem"
        },
        {
          "value": "lg:mb-32",
          "label": "8rem"
        },
        {
          "value": "lg:mb-64",
          "label": "16rem"
        }
      ],
      "default": "lg:mb-0"
    },
    {
      "type": "select",
      "id": "vertical_padding",
      "label": "Vertical Padding",
      "options": [
        {
          "value": "lg:py-0",
          "label": "None"
        },
        {
          "value": "lg:py-1",
          "label": ".25rem"
        },
        {
          "value": "lg:py-2",
          "label": ".5rem"
        },
        {
          "value": "lg:py-4",
          "label": "1rem"
        },
        {
          "value": "lg:py-8",
          "label": "2rem"
        },
        {
          "value": "lg:py-16",
          "label": "4rem"
        },
        {
          "value": "lg:py-32",
          "label": "8rem"
        },
        {
          "value": "lg:py-64",
          "label": "16rem"
        }
      ],
      "default": "lg:py-0"
    },
    {
      "type": "select",
      "id": "horizontal_padding",
      "label": "Horizontal Padding",
      "options": [
        {
          "value": "lg:px-0",
          "label": "None"
        },
        {
          "value": "lg:px-1",
          "label": ".25rem"
        },
        {
          "value": "lg:px-2",
          "label": ".5rem"
        },
        {
          "value": "lg:px-4",
          "label": "1rem"
        },
        {
          "value": "lg:px-8",
          "label": "2rem"
        },
        {
          "value": "lg:px-16",
          "label": "4rem"
        },
        {
          "value": "lg:px-32",
          "label": "8rem"
        },
        {
          "value": "lg:px-64",
          "label": "16rem"
        }
      ],
      "default": "lg:px-0"
    },
    {
      "type": "header",
      "content": "Section Spacing Settings (Mobile)"
    },
    {
      "type": "select",
      "id": "section_top_margin_mobile",
      "label": "Margin top",
      "options": [
        {
          "value": "mt-0",
          "label": "None"
        },
        {
          "value": "mt-1",
          "label": ".25rem"
        },
        {
          "value": "mt-2",
          "label": ".5rem"
        },
        {
          "value": "mt-4",
          "label": "1rem"
        },
        {
          "value": "mt-8",
          "label": "2rem"
        },
        {
          "value": "mt-16",
          "label": "4rem"
        },
        {
          "value": "mt-32",
          "label": "8rem"
        },
        {
          "value": "mt-64",
          "label": "16rem"
        }
      ],
      "default": "mt-0"
    },
    {
      "type": "select",
      "id": "section_bottom_margin_mobile",
      "label": "Margin bottom",
      "options": [
        {
          "value": "mb-0",
          "label": "None"
        },
        {
          "value": "mb-1",
          "label": ".25rem"
        },
        {
          "value": "mb-2",
          "label": ".5rem"
        },
        {
          "value": "mb-4",
          "label": "1rem"
        },
        {
          "value": "mb-8",
          "label": "2rem"
        },
        {
          "value": "mb-16",
          "label": "4rem"
        },
        {
          "value": "mb-32",
          "label": "8rem"
        },
        {
          "value": "mb-64",
          "label": "16rem"
        }
      ],
      "default": "mb-0"
    },
    {
      "type": "select",
      "id": "vertical_padding_mobile",
      "label": "Vertical Padding (Mobile)",
      "options": [
        {
          "value": "py-0",
          "label": "None"
        },
        {
          "value": "py-1",
          "label": ".25rem"
        },
        {
          "value": "py-2",
          "label": ".5rem"
        },
        {
          "value": "py-4",
          "label": "1rem"
        },
        {
          "value": "py-8",
          "label": "2rem"
        },
        {
          "value": "py-16",
          "label": "4rem"
        },
        {
          "value": "py-32",
          "label": "8rem"
        },
        {
          "value": "py-64",
          "label": "16rem"
        }
      ],
      "default": "py-0"
    },
    {
      "type": "select",
      "id": "horizontal_padding_mobile",
      "label": "Horizontal Padding (Mobile)",
      "options": [
        {
          "value": "px-0",
          "label": "None"
        },
        {
          "value": "px-1",
          "label": ".25rem"
        },
        {
          "value": "px-2",
          "label": ".5rem"
        },
        {
          "value": "px-4",
          "label": "1rem"
        },
        {
          "value": "px-8",
          "label": "2rem"
        },
        {
          "value": "px-16",
          "label": "4rem"
        },
        {
          "value": "px-32",
          "label": "8rem"
        },
        {
          "value": "px-64",
          "label": "16rem"
        }
      ],
      "default": "px-0"
    },
    {
      "type": "header",
      "content": "Interactivity"
    },
    {
      "type": "select",
      "id": "scroll_effect",
      "label": "Scroll Effect",
      "options": [
        {
          "value": "",
          "label": "None"
        },
        {
          "value": "fade-in in-view:fade-in",
          "label": "Fade In"
        },
        {
          "value": "fade-in-rise in-view:fade-in-rise",
          "label": "Fade In and Rise"
        }
      ],
      "default": ""
		},
		{
			"type": "select",
			"id": "image_effect",
			"label": "Image Effect",
			"options": [
				{
					"value": "",
					"label": "None"
				},
				{
					"value": "animate zoom-in",
					"label": "Zoom In"
				}
			],
			"default": ""
		},
    {
      "type": "header",
      "content": "Cursor Image Options"
    },
    {
      "type": "image_picker",
      "label": "Cursor Image",
      "id": "cursor_image"
    },
    {
      "type": "text",
      "label": "Cursor SVG",
      "id": "cursor_svg",
      "info": "Choose an SVG from your [files](/admin/settings/files) and paste the file name in the field above."
    },
    {
      "type": "text",
      "label": "Cursor Image/SVG Max width",
      "id": "cursor_image_width",
      "info": "Use a pixel or percentage value here."
    },
    {
      "type": "header",
      "content": "Cursor Typeface Options"
    },
    {
      "type": "textarea",
      "id": "cursor",
      "label": "Cursor"
    },
    {
      "type": "select",
      "id": "cursor_type",
      "label": "Typeface",
      "options": [
        {
          "value": "MonumentGrotesk-Regular",
          "label": "Monument Grotesk Regular"
        },
        {
          "value": "MonumentGrotesk-Medium",
          "label": "Monument Grotesk Medium "
        },
        {
          "value": "MonumentGrotesk-Medium-Italic",
          "label": "Monument Grotesk Medium Italic"
        },
        {
          "value": "MonumentGrotesk-Bold",
          "label": "Monument Grotesk Bold"
        },
        {
          "value": "ABC-Monument",
          "label": "ABC Monument"
        },
        {
          "value": "Libre-Caslon",
          "label": "Libre Caslon"
        }
      ],
			"default": "MonumentGrotesk-Regular"
		}
  ],
  "blocks": [
    {
      "name": "Grid item",
      "type": "content",
      "settings": [
        {
          "type": "header",
          "content": "Grid Item Settings"
        },
        {
          "type": "select",
          "id": "item_width",
          "label": "Grid Item Width (Desktop)",
          "options": [
            {
              "value": "lg:w-full",
              "label": "100%"
            },
            {
              "value": "lg:w-1/12",
              "label": "10%"
            },
            {
              "value": "lg:w-1/5",
              "label": "20%"
            },
            {
              "value": "lg:w-1/4",
              "label": "25%"
            },
            {
              "value": "lg:w-1/3",
              "label": "33%"
            },
            {
              "value": "lg:w-2/5",
              "label": "40%"
            },
            {
              "value": "lg:w-1/2",
              "label": "50%"
            },
            {
              "value": "lg:w-3/5",
              "label": "60%"
            },
            {
              "value": "lg:w-2/3",
              "label": "66%"
            },
            {
              "value": "lg:w-3/4",
              "label": "75%"
            },
            {
              "value": "lg:w-4/5",
              "label": "80%"
            },
            {
              "value": "lg:w-11/12",
              "label": "90%"
            }
          ],
          "default": "lg:w-1/2"
        },
        {
          "type": "select",
          "id": "item_width_mobile",
          "label": "Grid Item Width (Mobile)",
          "options": [
            {
              "value": "w-full",
              "label": "100%"
            },
            {
              "value": "w-1/4",
              "label": "25%"
            },
            {
              "value": "w-1/3",
              "label": "33%"
            },
            {
              "value": "w-1/2",
              "label": "50%"
            },
            {
              "value": "w-2/3",
              "label": "66%"
            },
            {
              "value": "w-3/4",
              "label": "75%"
            }
          ],
          "default": "w-full"
        },
        {
          "type": "header",
          "content": "Item Order Settings"
        },
        {
          "type": "select",
          "id": "item_order",
          "label": "Grid Item Order (Desktop)",
          "options": [
            {
              "value": "lg:order-none",
              "label": "Default"
            },
            {
              "value": "lg:order-1",
              "label": "1"
            },
            {
              "value": "lg:order-2",
              "label": "2"
            },
            {
              "value": "lg:order-3",
              "label": "3"
            },
            {
              "value": "lg:order-4",
              "label": "4"
            },
            {
              "value": "lg:order-5",
              "label": "5"
            },
            {
              "value": "lg:order-6",
              "label": "6"
            }
          ],
          "default": "lg:order-none"
        },
        {
          "type": "select",
          "id": "item_order_mobile",
          "label": "Grid Item Order (Mobile)",
          "options": [
            {
              "value": "order-none",
              "label": "Default"
            },
            {
              "value": "order-1",
              "label": "1"
            },
            {
              "value": "order-2",
              "label": "2"
            },
            {
              "value": "order-3",
              "label": "3"
            },
            {
              "value": "order-4",
              "label": "4"
            },
            {
              "value": "order-5",
              "label": "5"
            },
            {
              "value": "order-6",
              "label": "6"
            }
          ],
          "default": "order-none"
        },
        {
          "type": "header",
          "content": "Image Settings"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "select",
          "id": "image_position",
          "label": "Image Position",
          "info": "For section height settings of: Full height, Three Quarter Height, Half Height, and Quarter Height.",
          "options": [
            {
              "value": "object-left-top",
              "label": "Top Left"
            },
            {
              "value": "object-top",
              "label": "Top Center"
            },
            {
              "value": "object-right-top",
              "label": "Top Right"
            },
            {
              "value": "object-left",
              "label": "Middle Left"
            },
            {
              "value": "object-center",
              "label": "Center"
            },
            {
              "value": "object-right",
              "label": "Middle Right"
            },
            {
              "value": "object-left-bottom",
              "label": "Bottom Left"
            },
            {
              "value": "object-bottom",
              "label": "Bottom Center"
            },
            {
              "value": "object-right-bottom",
              "label": "Bottom Right"
            }
          ],
          "default": "object-center"
        },
        {
          "type": "image_picker",
          "id": "image_mobile",
          "label": "Image (mobile)"
        },
        {
          "type": "select",
          "id": "image_position_mobile",
          "label": "Image Position",
          "info": "For section height settings of: Full height, Three Quarter Height, Half Height, and Quarter Height.",
          "options": [
            {
              "value": "object-left-top",
              "label": "Top Left"
            },
            {
              "value": "object-top",
              "label": "Top Center"
            },
            {
              "value": "object-right-top",
              "label": "Top Right"
            },
            {
              "value": "object-left",
              "label": "Middle Left"
            },
            {
              "value": "object-center",
              "label": "Center"
            },
            {
              "value": "object-right",
              "label": "Middle Right"
            },
            {
              "value": "object-left-bottom",
              "label": "Bottom Left"
            },
            {
              "value": "object-bottom",
              "label": "Bottom Center"
            },
            {
              "value": "object-right-bottom",
              "label": "Bottom Right"
            }
          ],
          "default": "object-center"
        },
        {
          "type": "header",
          "content": "Video Settings"
        },
        {
          "type": "text",
          "id": "video_url",
          "label": "Video mp4 url",
          "info": "For section height settings of: Full height, Three Quarter Height, Half Height, and Quarter Height. To get mp4 file: 1) Go to vimeo.com, 2) Click on desired video, 3) Click on settings, 4) Click on Video file"
        },
        {
          "type": "text",
          "id": "video_url_mobile",
          "label": "Video mp4 url (Mobile)",
          "info": "For section height settings of: Full height, Three Quarter Height, Half Height, and Quarter Height. To get mp4 file: 1) Go to vimeo.com, 2) Click on desired video, 3) Click on settings, 4) Click on Video file"
        },
        {
          "type": "image_picker",
          "id": "poster_image",
          "label": "Video Poster Image"
        },
        {
          "type": "checkbox",
          "id": "show_video_controls",
          "label": "Show Video Controls",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "show_audio_controls",
          "label": "Show Audio Controls",
          "default": false
        },
        {
          "type": "header",
          "content": "Image Display Settings"
        },
        {
          "type": "select",
          "id": "corners",
          "label": "Slide Corners (Desktop)",
          "options": [
            {
              "value": "br0-l",
              "label": "Square"
            },
            {
              "value": "br5-l",
              "label": "Round Corners"
            },
            {
              "value": "br5-l br--top-right-l",
              "label": "Top left Round"
            },
            {
              "value": "br5-l br--top-left-l",
              "label": "Top Right Round"
            },
            {
              "value": "br5-l br--bottom-right-l",
              "label": "Bottom Left Round"
            },
            {
              "value": "br5-l br--bottom-left-l",
              "label": "Bottom Right Round"
            },
            {
              "value": "br6-l",
              "label": "Large Round Corners"
            },
            {
              "value": "br6-l br--top-right-l",
              "label": "Large Top left Round"
            },
            {
              "value": "br6-l br--top-left-l",
              "label": "Large Top Right Round"
            },
            {
              "value": "br6-l br--bottom-right-l",
              "label": "Large Bottom Left Round"
            },
            {
              "value": "br6-l br--bottom-left-l",
              "label": "Large Bottom Right Round"
            }
          ],
          "default": "br0-l"
        },
        {
          "type": "select",
          "id": "corners_mobile",
          "label": "Slide corners (Mobile)",
          "options": [
            {
              "value": "br0",
              "label": "Square"
            },
            {
              "value": "br5",
              "label": "Round Corners"
            },
            {
              "value": "br5 br--top-right",
              "label": "Top left Round"
            },
            {
              "value": "br5 br--top-left",
              "label": "Top Right Round"
            },
            {
              "value": "br5 br--bottom-right",
              "label": "Bottom Left Round"
            },
            {
              "value": "br5 br--bottom-left",
              "label": "Bottom Right Round"
            },
            {
              "value": "br6",
              "label": "Large Round Corners"
            },
            {
              "value": "br6 br--top-right",
              "label": "Large Top left Round"
            },
            {
              "value": "br6 br--top-left",
              "label": "Large Top Right Round"
            },
            {
              "value": "br6 br--bottom-right",
              "label": "Large Bottom Left Round"
            },
            {
              "value": "br6 br--bottom-left",
              "label": "Large Bottom Right Round"
            }
          ],
          "default": "br0"
        },
        {
          "type": "header",
          "content": "Overlay Settings"
        },
        {
          "type": "color",
          "id": "overlay",
          "label": "Overlay color"
        },
        {
          "type": "range",
          "id": "overlay_opacity",
          "label": "Overlay Opacity",
          "min": 10,
          "max": 100,
          "step": 10,
          "unit": "%",
          "default": 40
        },
        {
          "type": "image_picker",
          "id": "overlay_img",
          "label": "Overlay Image"
        },
        {
          "type": "header",
          "content": "Content Item Settings"
        },
        {
          "type": "select",
          "id": "content_direction",
          "label": "Direction (Desktop)",
          "options": [
            {
              "value": "lg:flex-row",
              "label": "Row"
            },
            {
              "value": "lg:flex-row-reverse",
              "label": "Row Reverse"
            },
            {
              "value": "lg:flex-col",
              "label": "Column"
            },
            {
              "value": "lg:flex-col-reverse",
              "label": "Column Reverse"
            }
          ],
          "default": "lg:flex-col"
        },
        {
          "type": "select",
          "id": "content_direction_mobile",
          "label": "Direction (Mobile)",
          "options": [
            {
              "value": "flex-row",
              "label": "Row"
            },
            {
              "value": "flex-row-reverse",
              "label": "Row Reverse"
            },
            {
              "value": "flex-col",
              "label": "Column"
            },
            {
              "value": "flex-col-reverse",
              "label": "Column Reverse"
            }
          ],
          "default": "flex-col"
        },
        {
          "type": "select",
          "id": "content_gap",
          "label": "Gap (Desktop)",
          "options": [
            {
              "value": "lg:gap-0",
              "label": "None"
            },
            {
              "value": "lg:gap-xs",
              "label": "XS"
            },
            {
              "value": "lg:gap-sm",
              "label": "SM"
            },
            {
              "value": "lg:gap-base",
              "label": "MD / Base"
            },
            {
              "value": "lg:gap-lg",
              "label": "LG"
            },
            {
              "value": "lg:gap-xl",
              "label": "XL"
            },
            {
              "value": "lg:gap-2xl",
              "label": "2XL"
            },
            {
              "value": "lg:gap-3xl",
              "label": "3XL"
            },
            {
              "value": "lg:gap-4xl",
              "label": "4XL"
            },
            {
              "value": "lg:gap-5xl",
              "label": "5XL"
            },
            {
              "value": "lg:gap-6xl",
              "label": "6XL"
            },
            {
              "value": "lg:gap-7xl",
              "label": "7XL"
            }
          ],
          "default": "lg:gap-base"
        },
        {
          "type": "select",
          "id": "content_gap_mobile",
          "label": "Gap (Mobile)",
          "options": [
            {
              "value": "gap-0",
              "label": "None"
            },
            {
              "value": "gap-xs",
              "label": "XS"
            },
            {
              "value": "gap-sm",
              "label": "SM"
            },
            {
              "value": "gap-base",
              "label": "MD / Base"
            },
            {
              "value": "gap-lg",
              "label": "LG"
            },
            {
              "value": "gap-xl",
              "label": "XL"
            },
            {
              "value": "gap-2xl",
              "label": "2XL"
            }
          ],
          "default": "gap-base"
        },
        {
          "type": "header",
          "content": "Text Settings"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text color"
        },
        {
          "type": "select",
          "id": "text_align",
          "label": "Text Alignment (Desktop)",
          "options": [
            {
              "value": "lg:text-left",
              "label": "Left"
            },
            {
              "value": "lg:text-center",
              "label": "Center"
            },
            {
              "value": "lg:text-right",
              "label": "Right"
            },
            {
              "value": "lg:text-justify lg:text-justify-last",
              "label": "Justify"
            }
          ],
          "default": "lg:text-left"
        },
        {
          "type": "select",
          "id": "text_align_mobile",
          "label": "Text Alignment (Mobile)",
          "options": [
            {
              "value": "text-left",
              "label": "Left"
            },
            {
              "value": "text-center",
              "label": "Center"
            },
            {
              "value": "text-right",
              "label": "Right"
            },
            {
              "value": "text-justify text-justify-last",
              "label": "Justify"
            }
          ],
          "default": "text-left"
        },
        {
          "type": "header",
          "content": "Title Image Options"
        },
        {
          "type": "image_picker",
          "label": "Title Image",
          "id": "title_image"
        },
        {
          "type": "text",
          "label": "Title SVG",
          "id": "title_svg",
          "info": "Choose an SVG from your [files](/admin/settings/files) and paste the file name in the field above."
        },
        {
          "type": "text",
          "label": "Title Image/SVG Max width",
          "id": "title_image_width",
          "info": "Use a pixel or percentage value here.",
          "default": "100%"
        },
        {
          "type": "header",
          "content": "Pretitle Typeface Options"
        },
        {
          "type": "text",
          "id": "pretitle",
          "label": "Pretitle"
        },
        {
          "type": "select",
          "id": "pretitle_type",
          "label": "Typeface",
          "options": [
            {
              "value": "MonumentGrotesk-Regular",
              "label": "Monument Grotesk Regular"
            },
            {
              "value": "MonumentGrotesk-Medium",
              "label": "Monument Grotesk Medium "
            },
            {
              "value": "MonumentGrotesk-Medium-Italic",
              "label": "Monument Grotesk Medium Italic"
            },
            {
              "value": "MonumentGrotesk-Bold",
              "label": "Monument Grotesk Bold"
            },
            {
              "value": "ABC-Monument",
              "label": "ABC Monument"
            },
            {
              "value": "font-pop",
              "label": "ABC Monument Grotesk Black"
            },
            {
              "value": "font-highlight",
              "label": "ABC Monument Grotesk Light"
            },
            {
              "value": "Libre-Caslon",
              "label": "Libre Caslon"
            }
          ],
          "default": "MonumentGrotesk-Regular"
        },
        {
          "type": "number",
          "id": "pretitle_size",
          "label": "Pretitle Font Size (Desktop)",
          "default": 11
        },
        {
          "type": "number",
          "id": "pretitle_size_mobile",
          "label": "Pretitle Font Size (Mobile)",
          "default": 11
        },
        {
          "type": "color",
          "id": "pretitle_color",
          "label": "Pretitle color",
          "default": "#504F4F"
        },
        {
          "type": "text",
          "id": "pretitle_leading",
          "label": "Preitle Line Height",
          "default": "1.38"
        },
        {
          "type": "text",
          "id": "pretitle_tracking",
          "label": "Pretitle Letter Spacing"
        },
        {
          "type": "header",
          "content": "Title Typeface Options"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title"
        },
        {
          "type": "select",
          "id": "title_type",
          "label": "Typeface",
          "options": [
            {
              "value": "MonumentGrotesk-Regular",
              "label": "Monument Grotesk Regular"
            },
            {
              "value": "MonumentGrotesk-Medium",
              "label": "Monument Grotesk Medium "
            },
            {
              "value": "MonumentGrotesk-Medium-Italic",
              "label": "Monument Grotesk Medium Italic"
            },
            {
              "value": "MonumentGrotesk-Bold",
              "label": "Monument Grotesk Bold"
            },
            {
              "value": "ABC-Monument",
              "label": "ABC Monument"
            },
            {
              "value": "font-pop",
              "label": "ABC Monument Grotesk Black"
            },
            {
              "value": "font-highlight",
              "label": "ABC Monument Grotesk Light"
            },
            {
              "value": "Libre-Caslon",
              "label": "Libre Caslon"
            }
          ],
          "default": "MonumentGrotesk-Bold"
        },
        {
          "type": "number",
          "id": "title_size",
          "label": "Title Font Size (Desktop)",
          "default": 60
        },
        {
          "type": "number",
          "id": "title_size_mobile",
          "label": "Title Font Size (Mobile)",
          "default": 50
        },
        {
          "type": "color",
          "id": "title_color",
          "label": "Title color",
          "default": "#000000"
        },
        {
          "type": "text",
          "id": "title_leading",
          "label": "Title Line Height",
          "default": "1.38"
        },
        {
          "type": "text",
          "id": "title_tracking",
          "label": "Title Letter Spacing"
        },
        {
          "type": "header",
          "content": "Subtitle Typeface Options"
        },
        {
          "type": "text",
          "id": "subtitle",
          "label": "Subtitle"
        },
        {
          "type": "select",
          "id": "subtitle_type",
          "label": "Typeface",
          "options": [
            {
              "value": "MonumentGrotesk-Regular",
              "label": "Monument Grotesk Regular"
            },
            {
              "value": "MonumentGrotesk-Medium",
              "label": "Monument Grotesk Medium "
            },
            {
              "value": "MonumentGrotesk-Medium-Italic",
              "label": "Monument Grotesk Medium Italic"
            },
            {
              "value": "MonumentGrotesk-Bold",
              "label": "Monument Grotesk Bold"
            },
            {
              "value": "ABC-Monument",
              "label": "ABC Monument"
            },
            {
              "value": "font-pop",
              "label": "ABC Monument Grotesk Black"
            },
            {
              "value": "font-highlight",
              "label": "ABC Monument Grotesk Light"
            },
            {
              "value": "Libre-Caslon",
              "label": "Libre Caslon"
            }
          ],
          "default": "MonumentGrotesk-Regular"
        },
        {
          "type": "number",
          "id": "subtitle_size",
          "label": "Subtitle Font Size (Desktop)",
          "default": 20
        },
        {
          "type": "number",
          "id": "subtitle_size_mobile",
          "label": "Subtitle Font Size (Mobile)",
          "default": 20
        },
        {
          "type": "color",
          "id": "subtitle_color",
          "label": "Subtitle color",
          "default": "#000000"
        },
        {
          "type": "text",
          "id": "subtitle_leading",
          "label": "Subtitle Line Height",
          "default": "1.38"
        },
        {
          "type": "text",
          "id": "subtitle_tracking",
          "label": "Subtitle Letter Spacing"
        },
        {
          "type": "header",
          "content": "Featured Content Typeface Options"
        },
        {
          "type": "richtext",
          "id": "featured_content",
          "label": "Featured Content"
        },
        {
          "type": "select",
          "id": "featured_content_type",
          "label": "Typeface",
          "options": [
            {
              "value": "MonumentGrotesk-Regular",
              "label": "Monument Grotesk Regular"
            },
            {
              "value": "MonumentGrotesk-Medium",
              "label": "Monument Grotesk Medium "
            },
            {
              "value": "MonumentGrotesk-Medium-Italic",
              "label": "Monument Grotesk Medium Italic"
            },
            {
              "value": "MonumentGrotesk-Bold",
              "label": "Monument Grotesk Bold"
            },
            {
              "value": "ABC-Monument",
              "label": "ABC Monument"
            },
            {
              "value": "font-pop",
              "label": "ABC Monument Grotesk Black"
            },
            {
              "value": "font-highlight",
              "label": "ABC Monument Grotesk Light"
            },
            {
              "value": "Libre-Caslon",
              "label": "Libre Caslon"
            }
          ],
          "default": "MonumentGrotesk-Regular"
        },
        {
          "type": "number",
          "id": "featured_content_size",
          "label": "Featured Content Font Size (Desktop)",
          "default": 16
        },
        {
          "type": "number",
          "id": "featured_content_size_mobile",
          "label": "Featured Content Font Size (Mobile)",
          "default": 16
        },
        {
          "type": "color",
          "id": "featured_content_color",
          "label": "Featured Content color",
          "default": "#000000"
        },
        {
          "type": "text",
          "id": "featured_leading",
          "label": "Featured Line Height",
          "default": "1.38"
        },
        {
          "type": "text",
          "id": "featured_tracking",
          "label": "Featured Letter Spacing"
        },
        {
          "type": "header",
          "content": "Content Typeface Options"
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Content"
        },
        {
          "type": "header",
          "content": "HTML Options"
        },
        {
          "type": "html",
          "id": "html",
          "label": "HTML"
        },
        {
          "type": "checkbox",
          "id": "grid_styling",
          "label": "Grid Styling",
          "default": true,
          "info": "Display HTML content in a 2-column layout."
        },
        {
          "type": "header",
          "content": "Link Settings"
        },
        {
          "type": "select",
          "id": "link_element",
          "label": "Link Element",
          "options": [
            {
              "value": "",
              "label": "Button Only"
            },
            {
              "value": "block",
              "label": "Entire Block"
            }
          ],
          "default": ""
        },
        {
          "type": "header",
          "content": "Button Alignment Settings"
        },
        {
          "type": "select",
          "id": "button_horizontal_align",
          "label": "Justify (Desktop)",
          "options": [
            {
              "value": "lg:justify-start",
              "label": "Start"
            },
            {
              "value": "lg:justify-center",
              "label": "Center"
            },
            {
              "value": "lg:justify-end",
              "label": "End"
            },
            {
              "value": "lg:justify-between",
              "label": "Between"
            }
          ],
          "default": "lg:justify-start"
        },
        {
          "type": "select",
          "id": "button_horizontal_align_mobile",
          "label": "Justify (Mobile)",
          "options": [
            {
              "value": "justify-start",
              "label": "Start"
            },
            {
              "value": "justify-center",
              "label": "Center"
            },
            {
              "value": "justify-end",
              "label": "End"
            },
            {
              "value": "justify-between",
              "label": "Between"
            }
          ],
          "default": "justify-start"
        },
        {
          "type": "select",
          "id": "button_vertical_align",
          "label": "Self Align (Desktop)",
          "options": [
            {
              "value": "lg:self-start",
              "label": "Start"
            },
            {
              "value": "lg:self-center",
              "label": "Center"
            },
            {
              "value": "lg:self-end",
              "label": "End"
            }
          ],
          "default": "lg:self-center"
        },
        {
          "type": "select",
          "id": "button_vertical_align_mobile",
          "label": "Self Align (Mobile)",
          "options": [
            {
              "value": "self-start",
              "label": "Start"
            },
            {
              "value": "self-center",
              "label": "Center"
            },
            {
              "value": "self-end",
              "label": "End"
            }
          ],
          "default": "self-center"
        },
        {
          "type": "header",
          "content": "Button 1 Layout Settings"
        },
        {
          "type": "text",
          "id": "link_title",
          "label": "Link title"
        },
        {
          "type": "url",
          "id": "url",
          "label": "Link"
        },
        {
          "type": "select",
          "id": "link_target",
          "label": "Link Target",
          "options": [
            {
              "value": "_blank",
              "label": "New Window"
            },
            {
              "value": "_self",
              "label": "Same Window"
            }
          ],
          "default": "_self"
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Button Style",
          "options": [
            {
              "value": "button button--primary",
              "label": "Button Primary"
            },
            {
              "value": "button button--secondary",
              "label": "Button Secondary"
            },
            {
              "value": "button button--tertiary",
              "label": "Button Tertiary"
            },
            {
              "value": "button button--light",
              "label": "Button Light"
            },
            {
              "value": "button button--higlight",
              "label": "Button Highlight"
            },
            {
              "value": "button button--link",
              "label": "Button Text"
            },
            {
              "value": "button button--text",
              "label": "Text link"
            }
          ],
          "default": "button button--primary"
        },
        {
          "type": "color",
          "id": "btn_color",
          "label": "Button bg color",
          "default": "#f7f7f7"
        },
        {
          "type": "color",
          "id": "btn_text_color",
          "label": "Button text color",
          "default": "#222222"
        },
        {
          "type": "color",
          "id": "btn_border_color",
          "label": "Button border color",
          "default": "#222222"
        },
        {
          "type": "color",
          "id": "btn_hover_color",
          "label": "Button bg color (hover)"
        },
        {
          "type": "color",
          "id": "btn_hover_text_color",
          "label": "Button text color (hover)"
        },
        {
          "type": "color",
          "id": "btn_hover_border_color",
          "label": "Button border color (hover)"
        },
        {
          "type": "number",
          "id": "button_size",
          "label": "Button Font Size",
          "info": "Optional"
        },
        {
          "type": "number",
          "id": "button_size_mobile",
          "label": "Button Font Size (Mobile)",
          "info": "Optional"
        },
        {
          "type": "header",
          "content": "Button 2 Layout Settings"
        },
        {
          "type": "text",
          "id": "link_title_two",
          "label": "Link title"
        },
        {
          "type": "url",
          "id": "url_two",
          "label": "Link"
        },
        {
          "type": "select",
          "id": "link_target_two",
          "label": "Link Target",
          "options": [
            {
              "value": "_blank",
              "label": "New Window"
            },
            {
              "value": "_self",
              "label": "Same Window"
            }
          ],
          "default": "_self"
        },
        {
          "type": "select",
          "id": "button_style_two",
          "label": "Button Style",
          "options": [
            {
              "value": "button button--primary",
              "label": "Button Primary"
            },
            {
              "value": "button button--secondary",
              "label": "Button Secondary"
            },
            {
              "value": "button button--tertiary",
              "label": "Button Tertiary"
            },
            {
              "value": "button button--light",
              "label": "Button Light"
            },
            {
              "value": "button button--higlight",
              "label": "Button Highlight"
            },
            {
              "value": "button button--link",
              "label": "Button Text"
            },
            {
              "value": "button button--text",
              "label": "Text link"
            }
          ],
          "default": "button button--primary"
        },
        {
          "type": "color",
          "id": "btn_color_two",
          "label": "Button bg color",
          "default": "#f7f7f7"
        },
        {
          "type": "color",
          "id": "btn_text_color_two",
          "label": "Button text color",
          "default": "#000000"
        },
        {
          "type": "color",
          "id": "btn_border_color_two",
          "label": "Button border color",
          "default": "#000000"
        },
        {
          "type": "color",
          "id": "btn_hover_color_two",
          "label": "Button bg color (hover)"
        },
        {
          "type": "color",
          "id": "btn_hover_text_color_two",
          "label": "Button text color (hover)"
        },
        {
          "type": "color",
          "id": "btn_hover_border_color_two",
          "label": "Button border color (hover)"
        },
        {
          "type": "number",
          "id": "button_size_two",
          "label": "Button Font Size",
          "info": "Optional"
        },
        {
          "type": "number",
          "id": "button_size_mobile_two",
          "label": "Button Font Size (Mobile)",
          "info": "Optional"
        },
        {
          "type": "header",
          "content": "Content Layout Settings"
        },
        {
          "type": "header",
          "content": "Desktop Content Layout Settings"
        },
        {
          "type": "select",
          "id": "content_width",
          "label": "Content Width",
          "options": [
            {
              "value": "lg:w-full",
              "label": "100%",
              "group": "Percentage"
            },
            {
              "value": "lg:w-3/4",
              "label": "75%",
              "group": "Percentage"
            },
            {
              "value": "lg:w-3/5",
              "label": "60%",
              "group": "Percentage"
            },
            {
              "value": "lg:w-1/2",
              "label": "50%",
              "group": "Percentage"
            },
            {
              "value": "lg:w-1/4",
              "label": "25%",
              "group": "Percentage"
            },
            {
              "value": "lg:max-w-xl",
              "label": "XS",
              "group": "Fixed"
            },
            {
              "value": "lg:max-w-2xl",
              "label": "SM",
              "group": "Fixed"
            },
            {
              "value": "lg:max-w-3xl",
              "label": "MD",
              "group": "Fixed"
            },
            {
              "value": "lg:max-w-4xl",
              "label": "LG",
              "group": "Fixed"
            },
            {
              "value": "lg:max-w-5xl",
              "label": "XL",
              "group": "Fixed"
            },
            {
              "value": "lg:max-w-6xl",
              "label": "2XL",
              "group": "Fixed"
            }
          ],
          "default": "lg:w-full"
        },
        {
          "type": "select",
          "id": "content_position",
          "label": "Content Position",
          "options": [
            {
              "value": "lg:items-start lg:justify-start",
              "label": "Top Left"
            },
            {
              "value": "lg:items-center lg:justify-start",
              "label": "Top Center"
            },
            {
              "value": "lg:items-end lg:justify-start",
              "label": "Top Right"
            },
            {
              "value": "lg:items-start lg:justify-center",
              "label": "Middle Left"
            },
            {
              "value": "lg:items-center lg:justify-center",
              "label": "Middle Center"
            },
            {
              "value": "lg:items-end lg:justify-center",
              "label": "Middle Right"
            },
            {
              "value": "lg:items-start lg:justify-end",
              "label": "Bottom Left"
            },
            {
              "value": "lg:items-center lg:justify-end",
              "label": "Bottom Center"
            },
            {
              "value": "lg:items-end lg:justify-end",
              "label": "Bottom Right"
            }
          ],
          "default": "lg:items-center lg:justify-center"
        },
        {
          "type": "select",
          "id": "content_height",
          "label": "Content Height",
          "options": [
            {
              "value": "h-full",
              "label": "100%"
            }, 
            {
              "value": "h-3/4",
              "label": "75%"
            }, 
            {
              "value": "h-1/2",
              "label": "50%"
            }, 
            {
              "value": "h-1/4",
              "label": "25%"
            }, 
            {
              "value": "h-auto",
              "label": "Auto"
            }
          ],
          "default": "h-auto"
        },
        {
          "type": "select",
          "id": "content_vertical_padding",
          "label": "Content Vertical Padding",
          "options": [
            {
              "value": "lg:py-0",
              "label": "None"
            },
            {
              "value": "lg:py-1",
              "label": ".25rem"
            },
            {
              "value": "lg:py-2",
              "label": ".5rem"
            },
            {
              "value": "lg:py-4",
              "label": "1rem"
            },
            {
              "value": "lg:py-8",
              "label": "2rem"
            },
            {
              "value": "lg:py-16",
              "label": "4rem"
            },
            {
              "value": "lg:py-32",
              "label": "8rem"
            },
            {
              "value": "lg:py-64",
              "label": "16rem"
            }
          ],
          "default": "lg:py-0"
        },
        {
          "type": "select",
          "id": "content_horizontal_padding",
          "label": "Content Horizontal Padding",
          "options": [
            {
              "value": "lg:px-0",
              "label": "None"
            },
            {
              "value": "lg:px-1",
              "label": ".25rem"
            },
            {
              "value": "lg:px-2",
              "label": ".5rem"
            },
            {
              "value": "lg:px-4",
              "label": "1rem"
            },
            {
              "value": "lg:px-8",
              "label": "2rem"
            },
            {
              "value": "lg:px-16",
              "label": "4rem"
            },
            {
              "value": "lg:px-32",
              "label": "8rem"
            },
            {
              "value": "lg:px-64",
              "label": "16rem"
            }
          ],
          "default": "lg:px-0"
        },
        {
          "type": "header",
          "content": "Mobile Content Layout Settings"
        },
        {
          "type": "select",
          "id": "content_position_mobile",
          "label": "Content Position",
          "options": [
            {
              "value": "h-full",
              "label": "None"
            },
            {
              "value": "items-center justify-start",
              "label": "Top"
            },
            {
              "value": "items-center justify-center",
              "label": "Middle"
            },
            {
              "value": "items-center justify-end",
              "label": "Bottom"
            }
          ],
          "default": "items-center justify-start"
        },
        {
          "type": "select",
          "id": "content_vertical_padding_mobile",
          "label": "Content Vertical Padding",
          "options": [
            {
              "value": "py-0",
              "label": "None"
            },
            {
              "value": "py-1",
              "label": ".25rem"
            },
            {
              "value": "py-2",
              "label": ".5rem"
            },
            {
              "value": "py-4",
              "label": "1rem"
            },
            {
              "value": "py-8",
              "label": "2rem"
            },
            {
              "value": "py-16",
              "label": "4rem"
            },
            {
              "value": "py-32",
              "label": "8rem"
            },
            {
              "value": "py-64",
              "label": "16rem"
            }
          ],
          "default": "py-0"
        },
        {
          "type": "select",
          "id": "content_horizontal_padding_mobile",
          "label": "Content Horizontal Padding",
          "options": [
            {
              "value": "px-0",
              "label": "None"
            },
            {
              "value": "px-1",
              "label": ".25rem"
            },
            {
              "value": "px-2",
              "label": ".5rem"
            },
            {
              "value": "px-4",
              "label": "1rem"
            },
            {
              "value": "px-8",
              "label": "2rem"
            },
            {
              "value": "px-16",
              "label": "4rem"
            },
            {
              "value": "px-32",
              "label": "8rem"
            },
            {
              "value": "px-64",
              "label": "16rem"
            }
          ],
          "default": "px-0"
        },
        {
          "type": "header",
          "content": "Desktop Text Format Settings"
        },
        {
          "type": "select",
          "id": "title_element",
          "label": "Heading Type",
          "options": [
            {
              "value": "h1",
              "label": "H1"
            },
            {
              "value": "h2",
              "label": "H2"
            },
            {
              "value": "h3",
              "label": "H3"
            },
            {
              "value": "h4",
              "label": "H4"
            },
            {
              "value": "h5",
              "label": "H5"
            }
          ],
          "default": "h2"
				},			
				{
					"type": "header",
					"content": "Interactivity"
				},
				{
					"type": "select",
					"id": "content_scroll_effect",
					"label": "Content Scroll Effect",
					"info": "Sticky based animations requires a Content Height greater than Auto",
					"options": [
						{
							"value": "",
							"label": "None"
						},
						{
							"value": "animate sticky-text",
							"label": "Sticky Text"
						},
						{
							"value": "animate text-fade-rise",
							"label": "Fade In Rise"
						},
						{
							"value": "animate text-fade-rise sticky-text",
							"label": "Sticky Fade In Rise"
						},
						{
							"value": "animate sticky-text invert-color",
							"label": "Sticky Invert Color"
						},
            {
              "value": "animate text-fade-rise sticky-text invert-color",
              "label": "Sticky Fade In with Invert Color"
            }
					],
					"default": ""
				}
      ]
    }
  ],
  "presets": [
    {
      "name": "Flexible Grid",
      "category": "Advanced",
      "settings": {},
      "blocks": []
    }
  ]
}
{% endschema %}

{% include 'async-section' skeleton:'<div class="p-16 w-full h-50v bg-white"><div class="bg-gray-100 p-16 rounded-2xl h-full w-full drop-shadow-lg flex flex-col items-center justify-center"><span class="sr-only">Skeleton</span><span class="bg-gray-200 h-24 w-24"></span><span class="bg-gray-200 h-2 w-1/4 mt-4"></span><span class="bg-gray-200 h-2 w-1/4 mt-4"></span><span class="bg-gray-200 h-2 w-1/4 mt-4"></span><span class="bg-gray-200 h-2 w-1/4 mt-4"></span></div></div>' %}
{% unless defer_section %}

<section class="section-{{section.id}} nav-transparent section-flexible-grid relative flex-column flex-wrap items-center justify-center {{ section.settings.section_top_margin }} {{ section.settings.section_bottom_margin }} {{ section.settings.section_top_margin_mobile }} {{ section.settings.section_bottom_margin_mobile }} {{ section.settings.vertical_padding }} {{ section.settings.vertical_padding_mobile }} {{ section.settings.horizontal_padding }} {{ section.settings.horizontal_padding_mobile }} {% if section.settings.show_desktop == true and section.settings.show_mobile == true %} flex {% elsif section.settings.show_desktop == true and section.settings.show_mobile == false %} hidden lg:flex {% elsif section.settings.show_desktop == false and section.settings.show_mobile == false %} hidden {% elsif section.settings.show_desktop == false and section.settings.show_mobile == true %} flex lg:flex lg:hidden {% endif %} {% if section.settings.remove_section_condition %} vwo-hide{% endif %}" {% if section.settings.background_color != blank %}style="background-color: {{ section.settings.background_color }}"{% endif %}
  {% if section.settings.cursor_image != blank or section.settings.cursor_svg != blank or section.settings.cursor != blank %}neptune-cursor="{text: 'this is my text', selector: '.custom-cursor'}"{% endif %}
  neptune-surface="{
    'direction':'up',
    'windowEdge': 'bottom',
    'elementEdge': 'top',
    'targets': [
      {
        'selector':'_self .flex-grid--container',
        'engage:action': {
          'classes': {
            'remove': ['active']
          }
        }
      }
    ]
  }"
> 
  {% if section.settings.cursor_image != blank or section.settings.cursor_svg != blank or section.settings.cursor != blank %}
    <div class="custom-cursor z-10" style="user-select:none;pointer-events:none;"> 
      {% if section.settings.cursor_image != blank %}
        <div class="dib" {% if section.settings.cursor_image_width != blank %}style="max-width: 100%; width: {{section.settings.cursor_image_width }};"{% endif %} alt="{{ section.settings.title }}">
          {% render 'lazy-image' with image: section.settings.cursor_image, image_class: "block" %}
          <img src="{{ section.settings.cursor_image | image_url }}" alt="">
        </div>
      {% endif %}
      {% if section.settings.cursor_svg != blank %}
        <img src="{{ section.settings.cursor_svg | file_url }}" class="inline-block" {% if section.settings.cursor_image_width != blank %}style="max-width: 100%; width: {{section.settings.cursor_image_width }};"{% endif %} alt="{{ section.settings.cursor }}">
      {% endif %}

      {% if section.settings.cursor != blank %}
        <span class="mb-2 {{ section.settings.cursor_type }}" 
        {% if section.settings.text_color != blank %}style="color: {{ section.settings.text_color }};"{% endif %}>
          {{ section.settings.cursor }}
        </span>                         
      {% endif %}
    </div>
  {% endif %}

  <div 
    class="flex-grid--container {{ section.settings.container }} {{ section.settings.scroll_effect }} animate animate-slow"
    neptune-surface="{
      'windowEdge': 'bottom',
      'elementEdge': 'top',
      'delay': '800',
      'targets': [
        {
          'engage:action': {
            'classes': {
              'add': ['active']
            }
          }
				}
      ]
    }"
  > 

	<div class="block-container flex flex-row flex-wrap lg:my-0 {{ section.settings.block_gutters | replace: 'ph', 'mh-' }} {{ section.settings.block_gutters_mobile | replace: 'pa', 'ma-' }} {% if section.settings.sticky != blank %}items-start{% endif %}"
		neptune-surface="{
      'windowEdge': 'bottom',
      'elementEdge': 'top',
      'targets': [
        {
          'engage:action': {
            'classes': {
              'add': ['active']
            }
          }
				}
      ]
    }"

		>
   
      {% for block in section.blocks %}

        {% if block.type == 'content'%}

				<article id="block-{{ block.id }}" class="relative {{ block.settings.content_scroll_effect }} {{ section.settings.height }} {{ section.settings.height_mobile }} {{ block.settings.item_width }} {{ block.settings.item_width_mobile }} lg:py-0 {{ section.settings.block_gutters }} {{ section.settings.block_gutters_mobile }} {{ block.settings.item_order }} {{ block.settings.item_order_mobile }} {% if section.settings.height contains 'content-below-height' or section.settings.height_mobile contains 'content-below-height' and block.settings.link_element != 'block' %}flex flex-col content-below-height{% else %} {% if section.settings.display_information  %} grid{% else %} block{% endif %}{% endif %} {% if section.settings.sticky != blank %}lg:sticky top-nav-l self-start{% endif %}">

            {% style %}
              @media screen and (max-width: 1023px) {
                .section-{{section.id}} .content-below-height .block__image_wrap.h-full {
                  height: auto;
                flex: 1;
                }
              }
            {% endstyle %}

            {% if block.settings.link_element == 'block' and block.settings.url != blank %}
						<a class="block w-full h-full link text-black relative {% if section.settings.height contains 'content-below-height' or section.settings.height_mobile contains 'content-below-height' %}flex flex-col{% else %}block{% endif %}" target="{{ block.settings.link_target }}" href="{{ block.settings.url }}">
            {% endif %}

              {% render 'content-item', sectionType: 'flexible-grid', section: section.settings, block: block.settings, blockId: block.id, sectionId: section.id, loop: forloop %}
            
            {% if block.settings.link_element == 'block' and block.settings.url != blank %}
              </a>
            {% endif %}

          </article>
        {% endif %}

      {% endfor %}
    </div>

  </div>
</section>

{% endunless %}
